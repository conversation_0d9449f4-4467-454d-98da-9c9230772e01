const fs = require('fs-extra');
const path = require('path');

async function testPDFDownload() {
  console.log('🧪 Тестирование скачивания PDF файлов...');
  
  const testDownloads = [
    {
      url: 'https://products.fst.com/medialibrary/materialdatasheets/75 FKM 585_en.pdf',
      filename: 'Material datasheet 75 FKM 585 (pdf)',
      type: 'pdf'
    },
    {
      url: 'https://products.fst.com/medialibrary/assets/productdatasheet_en_baum_e29c42deff.pdf',
      filename: 'Product datasheet (pdf)',
      type: 'pdf'
    }
  ];
  
  const productId = '386014';
  const outputDir = path.join(__dirname, 'test-output');
  const productDownloadsDir = path.join(outputDir, 'images', productId, 'downloads');
  
  // Создаем директории
  fs.ensureDirSync(productDownloadsDir);
  
  const localPaths = [];
  
  for (let i = 0; i < testDownloads.length; i++) {
    try {
      const download = testDownloads[i];
      console.log(`📄 Скачивание: ${download.filename}`);
      
      const response = await fetch(download.url);
      
      if (response.ok) {
        const buffer = await response.arrayBuffer();
        
        // Создаем безопасное имя файла
        const safeFilename = download.filename
          .replace(/[^a-zA-Z0-9\s\-_.()]/g, '_')
          .replace(/\s+/g, '_')
          .toLowerCase();
        
        const filename = safeFilename.endsWith('.pdf') ? safeFilename : `${safeFilename}.pdf`;
        const localPath = path.join(productDownloadsDir, filename);
        
        await fs.writeFile(localPath, Buffer.from(buffer));
        localPaths.push(path.relative(outputDir, localPath));
        
        const stats = await fs.stat(localPath);
        console.log(`   ✅ Сохранен: ${filename} (${Math.round(stats.size / 1024)} KB)`);
      } else {
        console.log(`   ❌ Ошибка HTTP ${response.status}: ${download.url}`);
      }
    } catch (error) {
      console.log(`   ❌ Ошибка скачивания: ${error.message}`);
    }
  }
  
  console.log('');
  console.log('📊 Результаты:');
  console.log(`   Скачано файлов: ${localPaths.length}/${testDownloads.length}`);
  console.log(`   Локальные пути:`);
  localPaths.forEach(path => {
    console.log(`     ${path}`);
  });
  
  // Проверяем что файлы действительно существуют
  console.log('');
  console.log('🔍 Проверка файлов:');
  for (const localPath of localPaths) {
    const fullPath = path.join(outputDir, localPath);
    if (fs.existsSync(fullPath)) {
      const stats = await fs.stat(fullPath);
      console.log(`   ✅ ${localPath} (${Math.round(stats.size / 1024)} KB)`);
    } else {
      console.log(`   ❌ ${localPath} - файл не найден`);
    }
  }
  
  console.log('');
  console.log('🎉 Тест завершен!');
}

testPDFDownload().catch(console.error);
