const { chromium } = require('playwright');

async function simpleTest() {
  console.log('🧪 Простой тест браузера...');
  
  try {
    const browser = await chromium.launch({ 
      headless: true,
      timeout: 30000,
      args: ['--no-sandbox', '--disable-dev-shm-usage']
    });
    
    console.log('✅ Браузер запущен');
    
    const page = await browser.newPage();
    console.log('✅ Страница создана');
    
    await page.goto('https://products.fst.com/global/en/categories/radial-shaft-seal-simmerring-baum/products/386014', {
      waitUntil: 'domcontentloaded',
      timeout: 30000
    });
    
    console.log('✅ Страница загружена');
    
    const title = await page.title();
    console.log(`📝 Заголовок: ${title}`);
    
    const productData = await page.evaluate(() => {
      const fstItemNo = window.location.pathname.split('/').pop() || '';
      const title = document.querySelector('h1')?.textContent?.trim() || document.title;
      
      // Ищем характеристики в новой структуре
      const specs = {};
      const dimensions = {};

      // Проходим по всем div элементам и ищем характеристики
      const allDivs = document.querySelectorAll('div');
      allDivs.forEach(div => {
        const text = div.textContent?.trim();
        if (!text) return;

        // Проверяем паттерны характеристик
        if (text.includes('GRM') && text.match(/[\d.]+\s*GRM/)) {
          specs['Net weight'] = text;
        } else if (text.includes('PCE') && text.match(/\d+\s*PCE/)) {
          specs['Packaging'] = text;
        } else if (text === 'Material not affected by REACH') {
          specs['REACH'] = text;
        } else if (text === 'COMPLIANT') {
          specs['ROHS'] = text;
        } else if (text === 'Freudenberg') {
          specs['Brand'] = text;
        } else if (text.includes('FKM')) {
          specs['Material'] = text;
        } else if (text.includes('434416')) {
          specs['Legacy Item No'] = text;
        }

      });

      // Отдельный поиск размеров по соседним элементам
      const allElements = document.querySelectorAll('*');
      allElements.forEach(el => {
        const text = el.textContent?.trim();
        if (!text) return;

        // Проверяем точные совпадения размеров
        if (text === '30 mm') {
          // Ищем соседние элементы для контекста
          const siblings = Array.from(el.parentElement?.children || []);
          const hasInnerDiameter = siblings.some(s => s.textContent?.includes('Inner diameter'));
          if (hasInnerDiameter) {
            dimensions['Inner diameter (d1)'] = text;
          }
        } else if (text === '47 mm') {
          const siblings = Array.from(el.parentElement?.children || []);
          const hasOuterDiameter = siblings.some(s => s.textContent?.includes('Outer diameter'));
          if (hasOuterDiameter) {
            dimensions['Outer diameter (d2)'] = text;
          }
        } else if (text === '7 mm') {
          const siblings = Array.from(el.parentElement?.children || []);
          const hasSealWidth = siblings.some(s => s.textContent?.includes('Seal width'));
          if (hasSealWidth) {
            dimensions['Seal width (b)'] = text;
          }
        }
      });
      
      // Ищем изображения
      const images = [];
      const imgs = document.querySelectorAll('img');
      imgs.forEach(img => {
        if (img.src && !img.src.includes('data:') && !img.src.includes('icon')) {
          images.push(img.src);
        }
      });

      // Ищем PDF файлы из секции Downloads
      const downloads = [];
      const pdfLinks = document.querySelectorAll('a[href*=".pdf"]');

      pdfLinks.forEach(link => {
        const href = link.getAttribute('href');
        const text = link.textContent?.trim();

        if (href && text) {
          const fullUrl = href.startsWith('http') ? href : `https://products.fst.com${href}`;

          // Проверяем что такой ссылки еще нет
          if (!downloads.some(d => d.url === fullUrl)) {
            downloads.push({
              url: fullUrl,
              filename: text,
              type: 'pdf'
            });
          }
        }
      });

      return {
        fstItemNo,
        title,
        specifications: specs,
        dimensions: dimensions,
        images: Array.from(new Set(images)),
        downloads,
        url: window.location.href
      };
    });
    
    console.log('📦 Данные товара:');
    console.log(`   ID: ${productData.fstItemNo}`);
    console.log(`   Название: ${productData.title}`);
    console.log(`   Характеристики: ${Object.keys(productData.specifications).length}`);
    console.log(`   Размеры: ${Object.keys(productData.dimensions).length}`);
    console.log(`   Изображения: ${productData.images.length}`);
    console.log(`   PDF файлы: ${productData.downloads.length}`);

    if (Object.keys(productData.specifications).length > 0) {
      console.log('🔧 Характеристики:');
      Object.entries(productData.specifications).forEach(([key, value]) => {
        console.log(`   ${key}: ${value}`);
      });
    }

    if (Object.keys(productData.dimensions).length > 0) {
      console.log('📏 Размеры:');
      Object.entries(productData.dimensions).forEach(([key, value]) => {
        console.log(`   ${key}: ${value}`);
      });
    }

    if (productData.downloads.length > 0) {
      console.log('📄 PDF файлы:');
      productData.downloads.forEach(download => {
        console.log(`   ${download.filename}: ${download.url}`);
      });
    }
    
    await browser.close();
    console.log('🎉 Тест завершен успешно!');
    
  } catch (error) {
    console.error('❌ Ошибка:', error.message);
  }
}

simpleTest();
