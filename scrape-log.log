{"timestamp":"2025-08-19T13:48:11.201Z","level":"INFO","message":"🚀 Запуск единого FST скраппера..."}
{"timestamp":"2025-08-19T13:48:11.202Z","level":"INFO","message":"🌐 Инициализация браузера..."}
{"timestamp":"2025-08-19T13:51:41.395Z","level":"WARNING","message":"⚠️ Попытка 1/3 инициализации браузера не удалась"}
{"timestamp":"2025-08-19T13:55:07.534Z","level":"WARNING","message":"⚠️ Попытка 2/3 инициализации браузера не удалась"}
{"timestamp":"2025-08-19T13:55:41.160Z","level":"INFO","message":"🚀 Запуск единого FST скраппера..."}
{"timestamp":"2025-08-19T13:55:41.160Z","level":"INFO","message":"🔍 Диагностика системы..."}
{"timestamp":"2025-08-19T13:55:41.163Z","level":"DEBUG","message":"📋 Node.js версия: v24.3.0"}
{"timestamp":"2025-08-19T13:55:41.163Z","level":"DEBUG","message":"📋 Платформа: win32 x64"}
{"timestamp":"2025-08-19T13:55:41.163Z","level":"DEBUG","message":"📋 Рабочая директория: D:\\Dev\\fst_scraper"}
{"timestamp":"2025-08-19T13:55:41.163Z","level":"DEBUG","message":"💾 Использование памяти: RSS=184MB, Heap=1MB"}
{"timestamp":"2025-08-19T13:55:41.164Z","level":"DEBUG","message":"🎭 Проверка Playwright..."}
{"timestamp":"2025-08-19T13:55:41.164Z","level":"DEBUG","message":"🎭 Playwright версия: 1.54.1"}
{"timestamp":"2025-08-19T13:55:41.164Z","level":"DEBUG","message":"🌐 Проверка установленных браузеров..."}
{"timestamp":"2025-08-19T13:55:41.164Z","level":"SUCCESS","message":"✅ Системная диагностика завершена"}
{"timestamp":"2025-08-19T13:55:41.165Z","level":"INFO","message":"🌐 Инициализация браузера..."}
{"timestamp":"2025-08-19T13:55:41.165Z","level":"INFO","message":"🔄 Попытка 1/3 запуска браузера..."}
{"timestamp":"2025-08-19T13:55:41.165Z","level":"DEBUG","message":"🔍 Проверка доступности Playwright..."}
{"timestamp":"2025-08-19T13:55:41.166Z","level":"DEBUG","message":"🚀 Запуск браузера с опциями: {\"headless\":true,\"timeout\":60000,\"args\":[\"--no-sandbox\",\"--disable-dev-shm-usage\",\"--disable-blink-features=AutomationControlled\",\"--disable-features=VizDisplayCompositor\",\"--disable-web-security\",\"--disable-background-timer-throttling\",\"--disable-backgrounding-occluded-windows\",\"--disable-renderer-backgrounding\"]}"}
{"timestamp":"2025-08-19T13:57:11.352Z","level":"ERROR","message":"❌ Ошибка попытки 1/3:","error":"TimeoutError: launch: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - <launching> C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1181\\chrome-win\\headless_shell.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --no-sandbox --disable-dev-shm-usage --disable-blink-features=AutomationControlled --disable-features=VizDisplayCompositor --disable-web-security --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Temp\\playwright_chromiumdev_profile-q9AV3b --remote-debugging-pipe --no-startup-window\u001b[22m\n\u001b[2m  - <launched> pid=31528\u001b[22m\n"}
{"timestamp":"2025-08-19T13:57:11.352Z","level":"ERROR","message":"📋 Детали ошибки: launch: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - <launching> C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1181\\chrome-win\\headless_shell.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --no-sandbox --disable-dev-shm-usage --disable-blink-features=AutomationControlled --disable-features=VizDisplayCompositor --disable-web-security --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Temp\\playwright_chromiumdev_profile-q9AV3b --remote-debugging-pipe --no-startup-window\u001b[22m\n\u001b[2m  - <launched> pid=31528\u001b[22m\n"}
{"timestamp":"2025-08-19T13:57:11.353Z","level":"ERROR","message":"📋 Stack trace: launch: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - <launching> C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1181\\chrome-win\\headless_shell.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --no-sandbox --disable-dev-shm-usage --disable-blink-features=AutomationControlled --disable-features=VizDisplayCompositor --disable-web-security --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Temp\\playwright_chromiumdev_profile-q9AV3b --remote-debugging-pipe --no-startup-window\u001b[22m\n\u001b[2m  - <launched> pid=31528\u001b[22m\n\n    at initBrowser (D:\\Dev\\fst_scraper\\src\\unified-scraper.ts:90:39)\n    at initBrowser (D:\\Dev\\fst_scraper\\src\\unified-scraper.ts:59:46)\n    at start (D:\\Dev\\fst_scraper\\src\\unified-scraper.ts:36:18)\n    at processTicksAndRejections (native:7:39)"}
{"timestamp":"2025-08-19T13:57:11.353Z","level":"INFO","message":"⏳ Ожидание 5 секунд перед следующей попыткой..."}
{"timestamp":"2025-08-19T13:57:16.360Z","level":"INFO","message":"🔄 Попытка 2/3 запуска браузера..."}
{"timestamp":"2025-08-19T13:57:16.360Z","level":"DEBUG","message":"🔍 Проверка доступности Playwright..."}
{"timestamp":"2025-08-19T13:57:16.360Z","level":"DEBUG","message":"🚀 Запуск браузера с опциями: {\"headless\":true,\"timeout\":60000,\"args\":[\"--no-sandbox\",\"--disable-dev-shm-usage\",\"--disable-blink-features=AutomationControlled\",\"--disable-features=VizDisplayCompositor\",\"--disable-web-security\",\"--disable-background-timer-throttling\",\"--disable-backgrounding-occluded-windows\",\"--disable-renderer-backgrounding\"]}"}
{"timestamp":"2025-08-19T16:40:24.597Z","level":"INFO","message":"🚀 Запуск единого FST скраппера..."}
{"timestamp":"2025-08-19T16:40:24.598Z","level":"INFO","message":"🔍 Диагностика системы..."}
{"timestamp":"2025-08-19T16:40:24.598Z","level":"DEBUG","message":"📋 Node.js версия: v24.3.0"}
{"timestamp":"2025-08-19T16:40:24.598Z","level":"DEBUG","message":"📋 Платформа: win32 x64"}
{"timestamp":"2025-08-19T16:40:24.598Z","level":"DEBUG","message":"📋 Рабочая директория: D:\\Dev\\fst_scraper"}
{"timestamp":"2025-08-19T16:40:24.598Z","level":"DEBUG","message":"💾 Использование памяти: RSS=195MB, Heap=15MB"}
{"timestamp":"2025-08-19T16:40:24.598Z","level":"DEBUG","message":"🎭 Проверка Playwright..."}
{"timestamp":"2025-08-19T16:40:24.599Z","level":"DEBUG","message":"🎭 Playwright версия: 1.54.2"}
{"timestamp":"2025-08-19T16:40:24.599Z","level":"DEBUG","message":"🌐 Проверка установленных браузеров..."}
{"timestamp":"2025-08-19T16:40:24.599Z","level":"SUCCESS","message":"✅ Системная диагностика завершена"}
{"timestamp":"2025-08-19T16:40:24.600Z","level":"INFO","message":"🌐 Инициализация браузера..."}
{"timestamp":"2025-08-19T16:40:24.600Z","level":"INFO","message":"🔄 Попытка 1/3 запуска браузера..."}
{"timestamp":"2025-08-19T16:40:24.600Z","level":"DEBUG","message":"🔍 Проверка доступности Playwright..."}
{"timestamp":"2025-08-19T16:40:24.600Z","level":"DEBUG","message":"🚀 Запуск браузера с опциями: {\"headless\":false,\"timeout\":30000,\"args\":[\"--no-sandbox\",\"--disable-dev-shm-usage\",\"--disable-web-security\",\"--disable-features=VizDisplayCompositor\",\"--disable-background-networking\",\"--disable-background-timer-throttling\",\"--disable-client-side-phishing-detection\",\"--disable-default-apps\",\"--disable-extensions\",\"--disable-hang-monitor\",\"--disable-popup-blocking\",\"--disable-prompt-on-repost\",\"--disable-sync\",\"--metrics-recording-only\",\"--no-first-run\",\"--safebrowsing-disable-auto-update\",\"--disable-component-extensions-with-background-pages\"]}"}
{"timestamp":"2025-08-19T16:41:24.938Z","level":"WARNING","message":"⚠️ Ошибка запуска с полными опциями: TimeoutError: launch: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - <launching> C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium-1181\\chrome-win\\chrome.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate,AutoDeElevate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --no-sandbox --no-sandbox --disable-dev-shm-usage --disable-web-security --disable-features=VizDisplayCompositor --disable-background-networking --disable-background-timer-throttling --disable-client-side-phishing-detection --disable-default-apps --disable-extensions --disable-hang-monitor --disable-popup-blocking --disable-prompt-on-repost --disable-sync --metrics-recording-only --no-first-run --safebrowsing-disable-auto-update --disable-component-extensions-with-background-pages --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Temp\\playwright_chromiumdev_profile-XYv2Fp --remote-debugging-pipe --no-startup-window\u001b[22m\n\u001b[2m  - <launched> pid=23808\u001b[22m\n"}
{"timestamp":"2025-08-19T16:41:24.938Z","level":"DEBUG","message":"🔄 Попытка запуска с минимальными опциями..."}
{"timestamp":"2025-08-19T16:42:10.115Z","level":"ERROR","message":"❌ Ошибка попытки 1/3:","error":"TimeoutError: launch: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - <launching> C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1181\\chrome-win\\headless_shell.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate,AutoDeElevate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --no-sandbox --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Temp\\playwright_chromiumdev_profile-mv3BEj --remote-debugging-pipe --no-startup-window\u001b[22m\n\u001b[2m  - <launched> pid=26724\u001b[22m\n"}
{"timestamp":"2025-08-19T16:42:10.115Z","level":"ERROR","message":"📋 Детали ошибки: launch: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - <launching> C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1181\\chrome-win\\headless_shell.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate,AutoDeElevate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --no-sandbox --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Temp\\playwright_chromiumdev_profile-mv3BEj --remote-debugging-pipe --no-startup-window\u001b[22m\n\u001b[2m  - <launched> pid=26724\u001b[22m\n"}
{"timestamp":"2025-08-19T16:42:10.115Z","level":"ERROR","message":"📋 Stack trace: launch: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - <launching> C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium_headless_shell-1181\\chrome-win\\headless_shell.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate,AutoDeElevate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --headless --hide-scrollbars --mute-audio --blink-settings=primaryHoverType=2,availableHoverTypes=2,primaryPointerType=4,availablePointerTypes=4 --no-sandbox --no-sandbox --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Temp\\playwright_chromiumdev_profile-mv3BEj --remote-debugging-pipe --no-startup-window\u001b[22m\n\u001b[2m  - <launched> pid=26724\u001b[22m\n\n    at initBrowser (D:\\Dev\\fst_scraper\\src\\unified-scraper.ts:113:41)\n    at processTicksAndRejections (native:7:39)"}
{"timestamp":"2025-08-19T16:42:10.115Z","level":"INFO","message":"⏳ Ожидание 5 секунд перед следующей попыткой..."}
{"timestamp":"2025-08-19T16:42:10.116Z","level":"DEBUG","message":"⏳ Ожидание 5000ms..."}
{"timestamp":"2025-08-19T16:42:15.127Z","level":"INFO","message":"🔄 Попытка 2/3 запуска браузера..."}
{"timestamp":"2025-08-19T16:42:15.127Z","level":"DEBUG","message":"🔍 Проверка доступности Playwright..."}
{"timestamp":"2025-08-19T16:42:15.129Z","level":"DEBUG","message":"🚀 Запуск браузера с опциями: {\"headless\":false,\"timeout\":30000,\"args\":[\"--no-sandbox\",\"--disable-dev-shm-usage\",\"--disable-web-security\",\"--disable-features=VizDisplayCompositor\",\"--disable-background-networking\",\"--disable-background-timer-throttling\",\"--disable-client-side-phishing-detection\",\"--disable-default-apps\",\"--disable-extensions\",\"--disable-hang-monitor\",\"--disable-popup-blocking\",\"--disable-prompt-on-repost\",\"--disable-sync\",\"--metrics-recording-only\",\"--no-first-run\",\"--safebrowsing-disable-auto-update\",\"--disable-component-extensions-with-background-pages\"]}"}
{"timestamp":"2025-08-19T16:43:24.537Z","level":"INFO","message":"🚀 Запуск единого FST скраппера..."}
{"timestamp":"2025-08-19T16:43:24.537Z","level":"INFO","message":"🔍 Диагностика системы..."}
{"timestamp":"2025-08-19T16:43:24.538Z","level":"DEBUG","message":"📋 Node.js версия: v24.3.0"}
{"timestamp":"2025-08-19T16:43:24.538Z","level":"DEBUG","message":"📋 Платформа: win32 x64"}
{"timestamp":"2025-08-19T16:43:24.538Z","level":"DEBUG","message":"📋 Рабочая директория: D:\\Dev\\fst_scraper"}
{"timestamp":"2025-08-19T16:43:24.538Z","level":"DEBUG","message":"💾 Использование памяти: RSS=184MB, Heap=1MB"}
{"timestamp":"2025-08-19T16:43:24.538Z","level":"DEBUG","message":"🎭 Проверка Playwright..."}
{"timestamp":"2025-08-19T16:43:24.539Z","level":"DEBUG","message":"🎭 Playwright версия: 1.54.2"}
{"timestamp":"2025-08-19T16:43:24.539Z","level":"DEBUG","message":"🌐 Проверка установленных браузеров..."}
{"timestamp":"2025-08-19T16:43:24.539Z","level":"SUCCESS","message":"✅ Системная диагностика завершена"}
{"timestamp":"2025-08-19T16:43:24.540Z","level":"INFO","message":"🌐 Инициализация браузера..."}
{"timestamp":"2025-08-19T16:43:24.540Z","level":"INFO","message":"🔄 Попытка 1/3 запуска браузера..."}
{"timestamp":"2025-08-19T16:43:24.540Z","level":"DEBUG","message":"🔍 Проверка доступности Playwright..."}
{"timestamp":"2025-08-19T16:43:24.540Z","level":"DEBUG","message":"🚀 Запуск браузера с опциями: {\"headless\":true,\"timeout\":60000,\"args\":[\"--no-sandbox\",\"--disable-dev-shm-usage\",\"--disable-web-security\",\"--disable-features=VizDisplayCompositor\"]}"}
{"timestamp":"2025-08-19T16:48:40.567Z","level":"INFO","message":"🚀 Запуск единого FST скраппера..."}
{"timestamp":"2025-08-19T16:48:40.567Z","level":"INFO","message":"🔍 Диагностика системы..."}
{"timestamp":"2025-08-19T16:48:40.567Z","level":"DEBUG","message":"📋 Node.js версия: v24.3.0"}
{"timestamp":"2025-08-19T16:48:40.567Z","level":"DEBUG","message":"📋 Платформа: win32 x64"}
{"timestamp":"2025-08-19T16:48:40.568Z","level":"DEBUG","message":"📋 Рабочая директория: D:\\Dev\\fst_scraper"}
{"timestamp":"2025-08-19T16:48:40.568Z","level":"DEBUG","message":"💾 Использование памяти: RSS=184MB, Heap=1MB"}
{"timestamp":"2025-08-19T16:48:40.568Z","level":"DEBUG","message":"🎭 Проверка Playwright..."}
{"timestamp":"2025-08-19T16:48:40.568Z","level":"DEBUG","message":"🎭 Playwright версия: 1.54.2"}
{"timestamp":"2025-08-19T16:48:40.569Z","level":"DEBUG","message":"🌐 Проверка установленных браузеров..."}
{"timestamp":"2025-08-19T16:48:40.569Z","level":"SUCCESS","message":"✅ Системная диагностика завершена"}
{"timestamp":"2025-08-19T16:48:40.569Z","level":"INFO","message":"🌐 Инициализация браузера..."}
{"timestamp":"2025-08-19T16:48:40.570Z","level":"INFO","message":"🔄 Попытка 1/3 запуска браузера..."}
{"timestamp":"2025-08-19T16:48:40.570Z","level":"DEBUG","message":"🔍 Проверка доступности Playwright..."}
{"timestamp":"2025-08-19T16:48:40.570Z","level":"DEBUG","message":"🚀 Запуск браузера с опциями: {\"headless\":false,\"timeout\":60000,\"args\":[\"--no-sandbox\",\"--disable-dev-shm-usage\",\"--disable-web-security\",\"--disable-features=VizDisplayCompositor\"]}"}
{"timestamp":"2025-08-19T16:52:40.180Z","level":"INFO","message":"🚀 Запуск единого FST скраппера..."}
{"timestamp":"2025-08-19T16:52:40.181Z","level":"INFO","message":"🔍 Диагностика системы..."}
{"timestamp":"2025-08-19T16:52:40.181Z","level":"DEBUG","message":"📋 Node.js версия: v24.3.0"}
{"timestamp":"2025-08-19T16:52:40.181Z","level":"DEBUG","message":"📋 Платформа: win32 x64"}
{"timestamp":"2025-08-19T16:52:40.181Z","level":"DEBUG","message":"📋 Рабочая директория: D:\\Dev\\fst_scraper"}
{"timestamp":"2025-08-19T16:52:40.181Z","level":"DEBUG","message":"💾 Использование памяти: RSS=184MB, Heap=1MB"}
{"timestamp":"2025-08-19T16:52:40.181Z","level":"DEBUG","message":"🎭 Проверка Playwright..."}
{"timestamp":"2025-08-19T16:52:40.182Z","level":"DEBUG","message":"🎭 Playwright версия: 1.54.2"}
{"timestamp":"2025-08-19T16:52:40.182Z","level":"DEBUG","message":"🌐 Проверка установленных браузеров..."}
{"timestamp":"2025-08-19T16:52:40.182Z","level":"SUCCESS","message":"✅ Системная диагностика завершена"}
{"timestamp":"2025-08-19T16:52:40.183Z","level":"INFO","message":"🌐 Инициализация браузера..."}
{"timestamp":"2025-08-19T16:52:40.183Z","level":"INFO","message":"🔄 Попытка 1/3 запуска браузера..."}
{"timestamp":"2025-08-19T16:52:40.183Z","level":"DEBUG","message":"🔍 Проверка доступности Playwright..."}
{"timestamp":"2025-08-19T16:52:40.183Z","level":"DEBUG","message":"🚀 Запуск браузера с опциями: {\"headless\":false,\"timeout\":60000,\"args\":[\"--no-sandbox\",\"--disable-dev-shm-usage\",\"--disable-web-security\",\"--disable-features=VizDisplayCompositor\"]}"}
{"timestamp":"2025-08-19T16:54:52.391Z","level":"INFO","message":"🚀 Запуск единого FST скраппера..."}
{"timestamp":"2025-08-19T16:54:52.391Z","level":"INFO","message":"🔍 Диагностика системы..."}
{"timestamp":"2025-08-19T16:54:52.391Z","level":"DEBUG","message":"📋 Node.js версия: v24.3.0"}
{"timestamp":"2025-08-19T16:54:52.391Z","level":"DEBUG","message":"📋 Платформа: win32 x64"}
{"timestamp":"2025-08-19T16:54:52.391Z","level":"DEBUG","message":"📋 Рабочая директория: D:\\Dev\\fst_scraper"}
{"timestamp":"2025-08-19T16:54:52.392Z","level":"DEBUG","message":"💾 Использование памяти: RSS=184MB, Heap=1MB"}
{"timestamp":"2025-08-19T16:54:52.392Z","level":"DEBUG","message":"🎭 Проверка Playwright..."}
{"timestamp":"2025-08-19T16:54:52.392Z","level":"DEBUG","message":"🎭 Playwright версия: 1.54.2"}
{"timestamp":"2025-08-19T16:54:52.392Z","level":"DEBUG","message":"🌐 Проверка установленных браузеров..."}
{"timestamp":"2025-08-19T16:54:52.393Z","level":"SUCCESS","message":"✅ Системная диагностика завершена"}
{"timestamp":"2025-08-19T16:54:52.393Z","level":"INFO","message":"🌐 Инициализация браузера..."}
{"timestamp":"2025-08-19T16:54:52.393Z","level":"INFO","message":"🔄 Попытка 1/3 запуска браузера..."}
{"timestamp":"2025-08-19T16:54:52.394Z","level":"DEBUG","message":"🔍 Проверка доступности Playwright..."}
{"timestamp":"2025-08-19T16:54:52.394Z","level":"DEBUG","message":"🚀 Запуск браузера с опциями: {\"headless\":false,\"timeout\":60000,\"args\":[\"--no-sandbox\",\"--disable-dev-shm-usage\",\"--disable-web-security\",\"--disable-features=VizDisplayCompositor\"]}"}
{"timestamp":"2025-08-19T18:41:51.903Z","level":"INFO","message":"🚀 Запуск единого FST скраппера..."}
{"timestamp":"2025-08-19T18:41:51.903Z","level":"INFO","message":"🔍 Диагностика системы..."}
{"timestamp":"2025-08-19T18:41:51.903Z","level":"DEBUG","message":"📋 Node.js версия: v24.3.0"}
{"timestamp":"2025-08-19T18:41:51.903Z","level":"DEBUG","message":"📋 Платформа: win32 x64"}
{"timestamp":"2025-08-19T18:41:51.903Z","level":"DEBUG","message":"📋 Рабочая директория: D:\\Dev\\fst_scraper"}
{"timestamp":"2025-08-19T18:41:51.904Z","level":"DEBUG","message":"💾 Использование памяти: RSS=184MB, Heap=1MB"}
{"timestamp":"2025-08-19T18:41:51.904Z","level":"DEBUG","message":"🎭 Проверка Playwright..."}
{"timestamp":"2025-08-19T18:41:51.904Z","level":"DEBUG","message":"🎭 Playwright версия: 1.54.2"}
{"timestamp":"2025-08-19T18:41:51.904Z","level":"DEBUG","message":"🌐 Проверка установленных браузеров..."}
{"timestamp":"2025-08-19T18:41:51.904Z","level":"SUCCESS","message":"✅ Системная диагностика завершена"}
{"timestamp":"2025-08-19T18:41:51.905Z","level":"INFO","message":"🌐 Инициализация браузера..."}
{"timestamp":"2025-08-19T18:41:51.905Z","level":"INFO","message":"🔄 Попытка 1/3 запуска браузера..."}
{"timestamp":"2025-08-19T18:41:51.905Z","level":"DEBUG","message":"🔍 Проверка доступности Playwright..."}
{"timestamp":"2025-08-19T18:41:51.905Z","level":"DEBUG","message":"🚀 Запуск браузера с опциями: {\"headless\":false,\"timeout\":60000,\"args\":[\"--no-sandbox\",\"--disable-dev-shm-usage\",\"--disable-web-security\",\"--disable-features=VizDisplayCompositor\"]}"}
{"timestamp":"2025-08-19T18:43:22.276Z","level":"WARNING","message":"⚠️ Ошибка запуска с полными опциями: TimeoutError: launch: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - <launching> C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium-1181\\chrome-win\\chrome.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate,AutoDeElevate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --no-sandbox --no-sandbox --disable-dev-shm-usage --disable-web-security --disable-features=VizDisplayCompositor --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Temp\\playwright_chromiumdev_profile-VOeE1b --remote-debugging-pipe --no-startup-window\u001b[22m\n\u001b[2m  - <launched> pid=21732\u001b[22m\n"}
{"timestamp":"2025-08-19T18:43:22.277Z","level":"DEBUG","message":"🔄 Попытка запуска с минимальными опциями..."}
{"timestamp":"2025-08-19T18:44:23.005Z","level":"ERROR","message":"❌ Ошибка попытки 1/3:","error":"TimeoutError: launch: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - <launching> C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium-1181\\chrome-win\\chrome.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate,AutoDeElevate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --no-sandbox --no-sandbox --disable-dev-shm-usage --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Temp\\playwright_chromiumdev_profile-oiuLLl --remote-debugging-pipe --no-startup-window\u001b[22m\n\u001b[2m  - <launched> pid=32984\u001b[22m\n"}
{"timestamp":"2025-08-19T18:44:23.006Z","level":"ERROR","message":"📋 Детали ошибки: launch: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - <launching> C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium-1181\\chrome-win\\chrome.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate,AutoDeElevate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --no-sandbox --no-sandbox --disable-dev-shm-usage --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Temp\\playwright_chromiumdev_profile-oiuLLl --remote-debugging-pipe --no-startup-window\u001b[22m\n\u001b[2m  - <launched> pid=32984\u001b[22m\n"}
{"timestamp":"2025-08-19T18:44:23.015Z","level":"ERROR","message":"📋 Stack trace: launch: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - <launching> C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium-1181\\chrome-win\\chrome.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate,AutoDeElevate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --no-sandbox --no-sandbox --disable-dev-shm-usage --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Temp\\playwright_chromiumdev_profile-oiuLLl --remote-debugging-pipe --no-startup-window\u001b[22m\n\u001b[2m  - <launched> pid=32984\u001b[22m\n\n    at initBrowser (D:\\Dev\\fst_scraper\\src\\unified-scraper.ts:102:41)\n    at processTicksAndRejections (native:7:39)"}
{"timestamp":"2025-08-19T18:44:23.019Z","level":"INFO","message":"⏳ Ожидание 5 секунд перед следующей попыткой..."}
{"timestamp":"2025-08-19T18:44:23.023Z","level":"DEBUG","message":"⏳ Ожидание 5000ms..."}
{"timestamp":"2025-08-19T18:44:28.039Z","level":"INFO","message":"🔄 Попытка 2/3 запуска браузера..."}
{"timestamp":"2025-08-19T18:44:28.042Z","level":"DEBUG","message":"🔍 Проверка доступности Playwright..."}
{"timestamp":"2025-08-19T18:44:28.063Z","level":"DEBUG","message":"🚀 Запуск браузера с опциями: {\"headless\":false,\"timeout\":60000,\"args\":[\"--no-sandbox\",\"--disable-dev-shm-usage\",\"--disable-web-security\",\"--disable-features=VizDisplayCompositor\"]}"}
{"timestamp":"2025-08-19T18:45:06.986Z","level":"INFO","message":"🚀 Запуск единого FST скраппера..."}
{"timestamp":"2025-08-19T18:45:06.986Z","level":"INFO","message":"🔍 Диагностика системы..."}
{"timestamp":"2025-08-19T18:45:06.986Z","level":"DEBUG","message":"📋 Node.js версия: v24.3.0"}
{"timestamp":"2025-08-19T18:45:06.986Z","level":"DEBUG","message":"📋 Платформа: win32 x64"}
{"timestamp":"2025-08-19T18:45:06.986Z","level":"DEBUG","message":"📋 Рабочая директория: D:\\Dev\\fst_scraper"}
{"timestamp":"2025-08-19T18:45:06.987Z","level":"DEBUG","message":"💾 Использование памяти: RSS=184MB, Heap=1MB"}
{"timestamp":"2025-08-19T18:45:06.987Z","level":"DEBUG","message":"🎭 Проверка Playwright..."}
{"timestamp":"2025-08-19T18:45:06.987Z","level":"DEBUG","message":"🎭 Playwright версия: 1.54.2"}
{"timestamp":"2025-08-19T18:45:06.987Z","level":"DEBUG","message":"🌐 Проверка установленных браузеров..."}
{"timestamp":"2025-08-19T18:45:06.987Z","level":"SUCCESS","message":"✅ Системная диагностика завершена"}
{"timestamp":"2025-08-19T18:45:06.988Z","level":"INFO","message":"🌐 Инициализация браузера..."}
{"timestamp":"2025-08-19T18:45:06.988Z","level":"INFO","message":"🔄 Попытка 1/3 запуска браузера..."}
{"timestamp":"2025-08-19T18:45:06.988Z","level":"DEBUG","message":"🔍 Проверка доступности Playwright..."}
{"timestamp":"2025-08-19T18:45:06.988Z","level":"DEBUG","message":"🚀 Запуск браузера с опциями: {\"headless\":false,\"timeout\":60000,\"args\":[\"--no-sandbox\",\"--disable-dev-shm-usage\",\"--disable-web-security\",\"--disable-features=VizDisplayCompositor\"]}"}
{"timestamp":"2025-08-19T19:00:24.546Z","level":"INFO","message":"🚀 Запуск единого FST скраппера..."}
{"timestamp":"2025-08-19T19:00:24.546Z","level":"INFO","message":"🔍 Диагностика системы..."}
{"timestamp":"2025-08-19T19:00:24.546Z","level":"DEBUG","message":"📋 Node.js версия: v24.3.0"}
{"timestamp":"2025-08-19T19:00:24.546Z","level":"DEBUG","message":"📋 Платформа: win32 x64"}
{"timestamp":"2025-08-19T19:00:24.547Z","level":"DEBUG","message":"📋 Рабочая директория: D:\\Dev\\fst_scraper"}
{"timestamp":"2025-08-19T19:00:24.547Z","level":"DEBUG","message":"💾 Использование памяти: RSS=184MB, Heap=1MB"}
{"timestamp":"2025-08-19T19:00:24.547Z","level":"DEBUG","message":"🎭 Проверка Playwright..."}
{"timestamp":"2025-08-19T19:00:24.547Z","level":"DEBUG","message":"🎭 Playwright версия: 1.54.2"}
{"timestamp":"2025-08-19T19:00:24.547Z","level":"DEBUG","message":"🌐 Проверка установленных браузеров..."}
{"timestamp":"2025-08-19T19:00:24.548Z","level":"SUCCESS","message":"✅ Системная диагностика завершена"}
{"timestamp":"2025-08-19T19:00:24.548Z","level":"INFO","message":"🌐 Инициализация браузера..."}
{"timestamp":"2025-08-19T19:00:24.548Z","level":"INFO","message":"🔄 Попытка 1/3 запуска браузера..."}
{"timestamp":"2025-08-19T19:00:24.549Z","level":"DEBUG","message":"🔍 Проверка доступности Playwright..."}
{"timestamp":"2025-08-19T19:00:24.549Z","level":"DEBUG","message":"🚀 Запуск браузера с опциями: {\"headless\":false,\"timeout\":60000,\"args\":[\"--no-sandbox\",\"--disable-dev-shm-usage\",\"--disable-web-security\",\"--disable-features=VizDisplayCompositor\"]}"}
{"timestamp":"2025-08-19T19:01:54.927Z","level":"WARNING","message":"⚠️ Ошибка запуска с полными опциями: TimeoutError: launch: Timeout 60000ms exceeded.\nCall log:\n\u001b[2m  - <launching> C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium-1181\\chrome-win\\chrome.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate,AutoDeElevate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --no-sandbox --no-sandbox --disable-dev-shm-usage --disable-web-security --disable-features=VizDisplayCompositor --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Temp\\playwright_chromiumdev_profile-h5SjDu --remote-debugging-pipe --no-startup-window\u001b[22m\n\u001b[2m  - <launched> pid=23752\u001b[22m\n"}
{"timestamp":"2025-08-19T19:01:54.928Z","level":"DEBUG","message":"🔄 Попытка запуска с минимальными опциями..."}
{"timestamp":"2025-08-19T19:02:55.240Z","level":"ERROR","message":"❌ Ошибка попытки 1/3:","error":"TimeoutError: launch: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - <launching> C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium-1181\\chrome-win\\chrome.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate,AutoDeElevate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --no-sandbox --no-sandbox --disable-dev-shm-usage --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Temp\\playwright_chromiumdev_profile-zbmLiQ --remote-debugging-pipe --no-startup-window\u001b[22m\n\u001b[2m  - <launched> pid=7728\u001b[22m\n"}
{"timestamp":"2025-08-19T19:02:55.242Z","level":"ERROR","message":"📋 Детали ошибки: launch: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - <launching> C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium-1181\\chrome-win\\chrome.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate,AutoDeElevate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --no-sandbox --no-sandbox --disable-dev-shm-usage --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Temp\\playwright_chromiumdev_profile-zbmLiQ --remote-debugging-pipe --no-startup-window\u001b[22m\n\u001b[2m  - <launched> pid=7728\u001b[22m\n"}
{"timestamp":"2025-08-19T19:02:55.242Z","level":"ERROR","message":"📋 Stack trace: launch: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - <launching> C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium-1181\\chrome-win\\chrome.exe --disable-field-trial-config --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-back-forward-cache --disable-breakpad --disable-client-side-phishing-detection --disable-component-extensions-with-background-pages --disable-component-update --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-features=AcceptCHFrame,AutoExpandDetailsElement,AvoidUnnecessaryBeforeUnloadCheckSync,CertificateTransparencyComponentUpdater,DestroyProfileOnBrowserClose,DialMediaRouteProvider,ExtensionManifestV2Disabled,GlobalMediaControls,HttpsUpgrades,ImprovedCookieControls,LazyFrameLoading,LensOverlay,MediaRouter,PaintHolding,ThirdPartyStoragePartitioning,Translate,AutoDeElevate --allow-pre-commit-input --disable-hang-monitor --disable-ipc-flooding-protection --disable-popup-blocking --disable-prompt-on-repost --disable-renderer-backgrounding --force-color-profile=srgb --metrics-recording-only --no-first-run --password-store=basic --use-mock-keychain --no-service-autorun --export-tagged-pdf --disable-search-engine-choice-screen --unsafely-disable-devtools-self-xss-warnings --edge-skip-compat-layer-relaunch --enable-automation --no-sandbox --no-sandbox --disable-dev-shm-usage --user-data-dir=C:\\Users\\<USER>\\AppData\\Local\\Temp\\playwright_chromiumdev_profile-zbmLiQ --remote-debugging-pipe --no-startup-window\u001b[22m\n\u001b[2m  - <launched> pid=7728\u001b[22m\n\n    at initBrowser (D:\\Dev\\fst_scraper\\src\\unified-scraper.ts:102:41)\n    at processTicksAndRejections (native:7:39)"}
{"timestamp":"2025-08-19T19:02:55.243Z","level":"INFO","message":"⏳ Ожидание 5 секунд перед следующей попыткой..."}
{"timestamp":"2025-08-19T19:02:55.244Z","level":"DEBUG","message":"⏳ Ожидание 5000ms..."}
{"timestamp":"2025-08-19T19:03:00.258Z","level":"INFO","message":"🔄 Попытка 2/3 запуска браузера..."}
{"timestamp":"2025-08-19T19:03:00.263Z","level":"DEBUG","message":"🔍 Проверка доступности Playwright..."}
{"timestamp":"2025-08-19T19:03:00.269Z","level":"DEBUG","message":"🚀 Запуск браузера с опциями: {\"headless\":false,\"timeout\":60000,\"args\":[\"--no-sandbox\",\"--disable-dev-shm-usage\",\"--disable-web-security\",\"--disable-features=VizDisplayCompositor\"]}"}
