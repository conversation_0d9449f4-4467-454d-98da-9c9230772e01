# 🚀 Единый FST Скраппер v2.0

Мощный скраппер для извлечения данных о продуктах с сайта FST (Freudenberg Sealing Technologies). Объединяет лучшие функции из всех предыдущих версий с улучшенной архитектурой.

## ✨ Особенности

- 🔍 **Полное извлечение данных**: характеристики, размеры, изображения, PDF файлы
- 📁 **Локальное сохранение изображений и PDF** с привязкой к товарам
- 🔄 **Возможность возобновления** после прерывания
- 📊 **Подробная статистика** и отслеживание прогресса
- 📝 **Детальное логирование** всех операций
- 🎯 **Цветное консольное логирование** для удобства
- 🛡️ **Обработка ошибок** и повторные попытки
- 💾 **Сохранение в JSON** с красивым форматированием

## Установка

```bash
# Установите зависимости
npm install

# Установите браузеры для Playwright
npx playwright install chromium
```

## Использование

### Основные команды

```bash
# Запуск скраппинга
npm start

# Проверка статуса
npm run status

# Сброс прогресса
npm run reset

# Показать справку
npm run help
```

### Альтернативные команды для разработки

```bash
npm run dev:start   # Запустить через ts-node (быстрее для разработки)
npm run dev:status  # Проверить статус через ts-node
npm run dev:reset   # Сбросить прогресс через ts-node
npm run build       # Скомпилировать TypeScript в JavaScript
```

## Структура проекта

```
fst-scraper/
├── src/
│   ├── scraper.ts      # Основной класс скраппера
│   ├── logger.ts       # Система журналирования прогресса
│   ├── types.ts        # TypeScript интерфейсы
│   └── cli.ts          # CLI интерфейс
├── data/               # Выходные данные
│   ├── [category].json # JSON файлы с информацией о продуктах по категориям
│   └── images/         # Папка с загруженными файлами
│       └── [product_id]/  # Файлы для каждого продукта
│           ├── image_1.jpg    # Изображения товара
│           ├── image_2.jpg
│           └── downloads/     # PDF файлы из секции Downloads
│               ├── material_datasheet.pdf
│               └── product_datasheet.pdf
├── config.json         # Конфигурация скраппера
├── scrape-log.json     # Журнал прогресса
├── package.json
├── tsconfig.json
└── README.md
```

## Структура данных продукта

Каждый продукт сохраняется в отдельном JSON файле со следующей структурой:

```json
{
  "fstItemNo": "386014",
  "legacyItemNo": "434416",
  "title": "Simmerring® | BAUM | 30 x 47 x 7 mm | 75 FKM 585 | 300 PCE",
  "description": "Radial Shaft Seal...",
  "specifications": {
    "Net weight": "14.9 GRM",
    "Packaging": "300 PCE",
    "REACH": "Material not affected by REACH",
    "ROHS": "COMPLIANT",
    "Brand": "Freudenberg",
    "Material": "75 FKM 585"
  },
  "dimensions": {
    "Inner diameter (d1)": "30 mm",
    "Outer diameter (d2)": "47 mm",
    "Seal width (b)": "7 mm"
  },
  "images": [
    "https://products.fst.com/image1.jpg"
  ],
  "localImages": [
    "images/386014/image_1.jpg"
  ],
  "downloads": [
    {
      "url": "https://products.fst.com/medialibrary/materialdatasheets/75 FKM 585_en.pdf",
      "filename": "Material datasheet 75 FKM 585 (pdf)",
      "type": "pdf"
    },
    {
      "url": "https://products.fst.com/medialibrary/assets/productdatasheet_en_baum.pdf",
      "filename": "Product datasheet (pdf)",
      "type": "pdf"
    }
  ],
  "localDownloads": [
    "images/386014/downloads/material_datasheet_75_fkm_585_(pdf).pdf",
    "images/386014/downloads/product_datasheet_(pdf).pdf"
  ],
  "category": "Radial Shaft Seal",
  "subcategory": "Simmerring BAUM",
  "url": "https://products.fst.com/.../386014",
  "scrapedAt": "2025-08-19T12:00:00.000Z"
    "Quantity": "300 PCE"
  },
  "breadcrumbs": ["Home", "Rotary Seals", "Simmerring®", "..."],
  "category": "Radial Shaft Seal Simmerring BAUM",
  "images": ["./data/images/386014/image_1.jpg", "..."],
  "metadata": {
    "keywords": "...",
    "description": "..."
  },
  "scrapedAt": "2025-01-21T14:35:06.789Z"
}
```

## Журналирование прогресса

Скраппер автоматически сохраняет прогресс в файл `scrape-log.json`. Это позволяет:

- Продолжить скраппинг после прерывания
- Отслеживать текущий статус
- Избежать повторного скраппинга уже обработанных продуктов

## Примеры использования

### Запуск скраппинга
```bash
bun run scrape:start
```

### Проверка прогресса во время работы
```bash
# В другом терминале
watch -n 5 'bun run scrape:status'
```

### Восстановление после прерывания
```bash
# Скраппинг был прерван, продолжаем с того места где остановились
bun run scrape:start
```

This project was created using `bun init` in bun v1.2.18. [Bun](https://bun.sh) is a fast all-in-one JavaScript runtime.
