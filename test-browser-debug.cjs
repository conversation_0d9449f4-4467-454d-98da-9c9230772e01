const { chromium } = require('playwright');

async function debugBrowser() {
  console.log('🔍 === ДИАГНОСТИКА БРАУЗЕРА ===');
  
  // Системная информация
  console.log(`📋 Node.js: ${process.version}`);
  console.log(`📋 Платформа: ${process.platform} ${process.arch}`);
  console.log(`📋 Рабочая директория: ${process.cwd()}`);
  
  // Память
  const memUsage = process.memoryUsage();
  console.log(`💾 Память: RSS=${Math.round(memUsage.rss / 1024 / 1024)}MB, Heap=${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);
  
  // Playwright версия
  try {
    const playwrightPkg = require('playwright/package.json');
    console.log(`🎭 Playwright: ${playwrightPkg.version}`);
  } catch (e) {
    console.log(`❌ Ошибка получения версии Playwright: ${e.message}`);
  }
  
  console.log('');
  console.log('🚀 === ТЕСТ ЗАПУСКА БРАУЗЕРА ===');
  
  let browser;
  let page;
  
  try {
    console.log('⏳ Шаг 1: Запуск браузера...');
    const startTime = Date.now();
    
    browser = await chromium.launch({
      headless: true,
      timeout: 60000,
      args: [
        '--no-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ]
    });
    
    const launchTime = Date.now() - startTime;
    console.log(`✅ Браузер запущен за ${launchTime}ms`);
    
    console.log('⏳ Шаг 2: Создание страницы...');
    page = await browser.newPage({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    });
    console.log('✅ Страница создана');
    
    console.log('⏳ Шаг 3: Настройка viewport...');
    await page.setViewportSize({ width: 1920, height: 1080 });
    page.setDefaultTimeout(30000);
    console.log('✅ Viewport настроен');
    
    console.log('⏳ Шаг 4: Переход на тестовую страницу...');
    const gotoStartTime = Date.now();
    
    await page.goto('https://products.fst.com/global/en', {
      waitUntil: 'domcontentloaded',
      timeout: 30000
    });
    
    const gotoTime = Date.now() - gotoStartTime;
    console.log(`✅ Страница загружена за ${gotoTime}ms`);
    
    console.log('⏳ Шаг 5: Проверка содержимого...');
    const title = await page.title();
    const url = page.url();
    
    console.log(`📄 Заголовок: ${title}`);
    console.log(`🌐 URL: ${url}`);
    
    // Проверка элементов на странице
    const bodyText = await page.evaluate(() => document.body.textContent?.substring(0, 100));
    console.log(`📝 Содержимое (первые 100 символов): ${bodyText}`);
    
    console.log('');
    console.log('🎉 === ВСЕ ТЕСТЫ ПРОШЛИ УСПЕШНО ===');
    
  } catch (error) {
    console.log('');
    console.log('❌ === ОШИБКА ===');
    console.log(`💥 Тип ошибки: ${error.constructor.name}`);
    console.log(`📋 Сообщение: ${error.message}`);
    
    if (error.stack) {
      console.log('📋 Stack trace:');
      console.log(error.stack);
    }
    
    // Дополнительная диагностика
    if (error.message.includes('timeout') || error.message.includes('Timeout')) {
      console.log('');
      console.log('🔍 === ДИАГНОСТИКА TIMEOUT ===');
      console.log('Возможные причины:');
      console.log('1. Медленное интернет-соединение');
      console.log('2. Блокировка сайтом (антибот защита)');
      console.log('3. Проблемы с DNS');
      console.log('4. Недостаток ресурсов системы');
    }
    
    if (error.message.includes('browser') || error.message.includes('Browser')) {
      console.log('');
      console.log('🔍 === ДИАГНОСТИКА БРАУЗЕРА ===');
      console.log('Возможные причины:');
      console.log('1. Не установлен браузер Chromium для Playwright');
      console.log('2. Недостаток прав доступа');
      console.log('3. Антивирус блокирует запуск');
      console.log('4. Недостаток места на диске');
      console.log('');
      console.log('Попробуйте выполнить:');
      console.log('bunx playwright install chromium');
    }
    
  } finally {
    if (page) {
      console.log('🔒 Закрытие страницы...');
      await page.close();
    }
    
    if (browser) {
      console.log('🔒 Закрытие браузера...');
      await browser.close();
    }
    
    console.log('✅ Очистка завершена');
  }
}

debugBrowser().catch(console.error);
