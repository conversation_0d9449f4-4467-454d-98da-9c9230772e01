#!/bin/bash

# FST Products Scraper - Quick Start Script

echo "🚀 FST Products Scraper"
echo "======================="
echo

# Check if bun is installed
if ! command -v bun &> /dev/null; then
    echo "❌ Bun is not installed. Please install Bun first:"
    echo "curl -fsSL https://bun.sh/install | bash"
    exit 1
fi

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    bun install
    echo
fi

# Check if playwright browsers are installed
if [ ! -d "$HOME/.cache/ms-playwright" ]; then
    echo "🌐 Installing Playwright browsers..."
    bunx playwright install
    echo
fi

# Show current status
echo "📊 Current Status:"
bun run scrape:status
echo

# Ask user what to do
echo "What would you like to do?"
echo "1) Start/Resume scraping"
echo "2) Check status"
echo "3) Reset progress"
echo "4) Exit"
echo
read -p "Enter your choice (1-4): " choice

case $choice in
    1)
        echo "🔄 Starting scraper..."
        bun run scrape:start
        ;;
    2)
        echo "📊 Checking status..."
        bun run scrape:status
        ;;
    3)
        read -p "⚠️  Are you sure you want to reset all progress? (y/N): " confirm
        if [[ $confirm == [yY] ]]; then
            echo "🔄 Resetting progress..."
            bun run scrape:reset
        else
            echo "❌ Reset cancelled"
        fi
        ;;
    4)
        echo "👋 Goodbye!"
        exit 0
        ;;
    *)
        echo "❌ Invalid choice"
        exit 1
        ;;
esac
