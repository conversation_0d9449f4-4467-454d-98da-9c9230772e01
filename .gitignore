# dependencies (bun install)
node_modules

data
dist

# output
out
dist
*.tgz

# code coverage
coverage
*.lcov

# logs
logs
_.log
report.[0-9]_.[0-9]_.[0-9]_.[0-9]_.json

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# caches
.eslintcache
.cache
*.tsbuildinfo

# IntelliJ based IDEs
.idea

# Finder (MacOS) folder config
.DS_Store

# FST Scraper specific files
# Uncomment the next lines if you don't want to track scraped data and logs
# data/
# scrape-log.json

# Playwright browsers cache
.playwright/

# IDE
.vscode/
*.swp
*.swo
