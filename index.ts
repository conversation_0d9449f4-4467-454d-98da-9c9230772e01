#!/usr/bin/env node

/**
 * 🚀 Единый FST Скраппер
 *
 * Основной файл для запуска скраппера FST продуктов.
 * Объединяет лучшие функции из всех предыдущих версий:
 * - Подробное логирование
 * - Локальное сохранение изображений
 * - Возможность возобновления после прерывания
 * - Детальная статистика прогресса
 *
 * Использование:
 *   bun index.ts              - Показать справку
 *   bun index.ts start        - Запустить скрапинг
 *   bun index.ts status       - Показать статус
 *   bun index.ts reset        - Сбросить прогресс
 */

import { UnifiedFSTScraper } from './src/unified-scraper.js';
import { ScrapingLogger } from './src/logger.js';
import * as path from 'path';
import * as fs from 'fs-extra';

const args = process.argv.slice(2);
const command = args[0];

async function main() {
  const rootDir = __dirname;
  const outputDir = path.join(rootDir, 'data');
  const logFile = path.join(rootDir, 'scrape-log.json');

  // Загружаем конфигурацию если есть
  let externalConfig: Partial<{
    baseUrl: string;
    delay: number;
    maxRetries: number;
  }> = {};

  const configPath = path.join(rootDir, 'config.json');
  if (fs.existsSync(configPath)) {
    try {
      externalConfig = await fs.readJSON(configPath);
      console.log('📋 Загружена конфигурация из config.json');
    } catch (e) {
      console.warn('⚠️ Не удалось прочитать config.json, используем настройки по умолчанию');
    }
  }

  const scraper = new UnifiedFSTScraper({
    baseUrl: externalConfig.baseUrl || 'https://products.fst.com/global/en',
    outputDir,
    logFile,
    delay: externalConfig.delay ?? 2000,
    maxRetries: externalConfig.maxRetries ?? 3
  });

  const logger = new ScrapingLogger(logFile);

  switch (command) {
    case 'start':
      console.log('🚀 Запуск единого FST скраппера...');
      console.log(`📁 Данные будут сохранены в: ${outputDir}`);
      console.log(`📝 Логи будут записаны в: ${logFile}`);
      console.log('');
      await scraper.start();
      break;

    case 'reset':
      console.log('🔄 Сброс прогресса скраппера...');
      logger.reset();
      console.log('✅ Прогресс успешно сброшен');
      break;

    case 'status':
      const progress = logger.getProgress();
      const stats = logger.getCompletionStats();

      console.log('');
      console.log('📊 === СТАТУС СКРАПИНГА ===');
      console.log(`🔄 Статус: ${getStatusEmoji(progress.status)} ${progress.status.toUpperCase()}`);
      console.log(`⏰ Начат: ${new Date(progress.startedAt).toLocaleString('ru-RU')}`);
      console.log(`🕐 Обновлен: ${new Date(progress.lastUpdatedAt).toLocaleString('ru-RU')}`);
      console.log('');
      console.log('📈 === ПРОГРЕСС ===');
      console.log(`📂 Категории: ${progress.scrapedCategories}/${progress.totalCategories}`);
      console.log(`📦 Товары: ${progress.scrapedProducts}/${progress.totalProducts} (${stats.percentage}%)`);

      if (progress.currentCategory) {
        console.log(`🎯 Текущая категория: ${progress.currentCategory}`);
      }

      if (progress.failedProducts.length > 0) {
        console.log(`❌ Неудачные товары: ${progress.failedProducts.length}`);
      }

      console.log('');
      break;

    default:
      showHelp();
      break;
  }
}

function getStatusEmoji(status: string): string {
  switch (status) {
    case 'running': return '🔄';
    case 'completed': return '✅';
    case 'paused': return '⏸️';
    case 'error': return '❌';
    default: return '❓';
  }
}

function showHelp() {
  console.log(`
🚀 Единый FST Скраппер v2.0

📋 ОПИСАНИЕ:
   Мощный скраппер для извлечения данных о продуктах с сайта FST.
   Включает подробное логирование, локальное сохранение изображений
   и возможность возобновления работы после прерывания.

🎯 ИСПОЛЬЗОВАНИЕ:
   bun index.ts [команда]

📝 КОМАНДЫ:
   start     🚀 Запустить или продолжить скрапинг
   status    📊 Показать текущий прогресс и статистику
   reset     🔄 Сбросить прогресс и начать заново
   help      ❓ Показать эту справку

💡 ПРИМЕРЫ:
   bun index.ts start        # Запустить скрапинг
   bun index.ts status       # Проверить прогресс
   bun index.ts reset        # Начать заново

🔧 КОНФИГУРАЦИЯ:
   Создайте файл config.json для настройки:
   {
     "baseUrl": "https://products.fst.com/global/en",
     "delay": 2000,
     "maxRetries": 3
   }

📁 СТРУКТУРА ДАННЫХ:
   data/                     # Основная папка с данными
   ├── images/              # Локально сохраненные изображения
   │   └── [productId]/     # Папки по ID товаров
   ├── [category].json      # Файлы категорий с товарами
   └── scrape-log.json      # Файл прогресса

📝 ЛОГИ:
   scrape-log.json          # JSON прогресс
   scrape-log.log           # Подробные текстовые логи

✨ ОСОБЕННОСТИ:
   ✅ Подробное логирование всех операций
   ✅ Автоматическое скачивание и сохранение изображений
   ✅ Возможность возобновления после прерывания
   ✅ Детальная статистика и прогресс
   ✅ Обработка ошибок и повторные попытки
   ✅ Цветное консольное логирование
  `);
}

main().catch((error) => {
  console.error('💥 Критическая ошибка:', error);
  process.exit(1);
});