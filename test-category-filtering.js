// Тестовый скрипт для проверки функции фильтрации категорий
console.log('🧪 Тестирование функции фильтрации категорий...\n');

// Мок данные категорий на основе реальной структуры FST
const mockCategories = [
  { name: 'Rotary Seals / Simmerring®', url: '/global/en/categories/rotary-seals/radial-shaft-seal-simmerring' },
  { name: 'Rotary Seals / Radiamatic®', url: '/global/en/categories/rotary-seals/radial-shaft-seal-radiamatic' },
  { name: 'Rotary Seals / Radial Shaft Seals', url: '/global/en/categories/rotary-seals/radial-shaft-seals' },
  { name: 'Rotary Seals / Cassette & Combi Seals', url: '/global/en/categories/rotary-seals/cassette-combi-seals' },
  { name: 'Rotary Seals / Axial Seals', url: '/global/en/categories/rotary-seals/axial-seals' },
  { name: 'Rotary Seals / Shaft Repair Sleeves', url: '/global/en/categories/rotary-seals/shaft-repair-sleeves' },
  { name: 'Rotary Seals / End Caps', url: '/global/en/categories/rotary-seals/end-caps' },
  { name: 'Hydraulics / Rod Seals (Hydraulic)', url: '/global/en/categories/hydraulics/rod-seals-hydraulic' },
  { name: 'Hydraulics / Piston Seals (Hydraulic)', url: '/global/en/categories/hydraulics/piston-seals-hydraulic' },
  { name: 'Static Seals / O-Rings', url: '/global/en/categories/static-seals/o-rings' },
  { name: 'Static Seals / X-Rings', url: '/global/en/categories/static-seals/x-rings' },
  { name: 'Static Seals / Cords', url: '/global/en/categories/static-seals/cords' },
  { name: 'Packings / Stuffing Box Packings', url: '/global/en/categories/packings/stuffing-box-packings' },
  { name: 'Packings / Tools', url: '/global/en/categories/packings/tools' }
];

// Функция фильтрации категорий (копия из основного кода)
function filterCategoriesToProcess(categories, config) {
  let filteredCategories = [...categories];

  // Пропускаем указанные категории
  if (config.skipCategories && config.skipCategories.length > 0) {
    const skipSet = new Set(config.skipCategories.map(name => name.toLowerCase()));
    filteredCategories = filteredCategories.filter(category => {
      const shouldSkip = skipSet.has(category.name.toLowerCase());
      if (shouldSkip) {
        console.log(`⏭️ Пропускаем категорию: ${category.name}`);
      }
      return !shouldSkip;
    });
  }

  // Начинаем с указанной категории
  if (config.startFromCategory) {
    const startFromIndex = filteredCategories.findIndex(
      category => category.name.toLowerCase() === config.startFromCategory.toLowerCase()
    );
    
    if (startFromIndex >= 0) {
      filteredCategories = filteredCategories.slice(startFromIndex);
      console.log(`🎯 Начинаем с категории: ${config.startFromCategory} (пропущено ${startFromIndex} категорий)`);
    } else {
      console.log(`⚠️ Категория "${config.startFromCategory}" не найдена, начинаем с первой доступной`);
    }
  }

  return filteredCategories;
}

console.log(`📂 Исходный список категорий (${mockCategories.length}):`);
mockCategories.forEach((category, index) => {
  console.log(`${index + 1}. ${category.name}`);
});

console.log('\n' + '='.repeat(80));

// Тест 1: Пропуск первой категории (завершенной)
console.log('\n🧪 Тест 1: Пропуск завершенной категории "Rotary Seals / Simmerring®"');
const test1Config = { skipCategories: ['Rotary Seals / Simmerring®'] };
const test1Result = filterCategoriesToProcess(mockCategories, test1Config);
console.log(`✅ Результат: ${test1Result.length} категорий (было ${mockCategories.length})`);
console.log(`📋 Первая категория теперь: ${test1Result[0]?.name}`);

console.log('\n' + '='.repeat(80));

// Тест 2: Начать с определенной категории
console.log('\n🧪 Тест 2: Начать с категории "Hydraulics / Rod Seals (Hydraulic)"');
const test2Config = { startFromCategory: 'Hydraulics / Rod Seals (Hydraulic)' };
const test2Result = filterCategoriesToProcess(mockCategories, test2Config);
console.log(`✅ Результат: ${test2Result.length} категорий`);
console.log(`📋 Первая категория: ${test2Result[0]?.name}`);

console.log('\n' + '='.repeat(80));

// Тест 3: Комбинация пропуска и начала
console.log('\n🧪 Тест 3: Пропуск нескольких категорий + начать с определенной');
const test3Config = { 
  skipCategories: ['Rotary Seals / Simmerring®', 'Rotary Seals / Radiamatic®'],
  startFromCategory: 'Rotary Seals / Axial Seals'
};
const test3Result = filterCategoriesToProcess(mockCategories, test3Config);
console.log(`✅ Результат: ${test3Result.length} категорий`);
console.log(`📋 Первая категория: ${test3Result[0]?.name}`);

console.log('\n' + '='.repeat(80));

// Тест 4: Несуществующая категория
console.log('\n🧪 Тест 4: Несуществующая категория для начала');
const test4Config = { startFromCategory: 'Несуществующая категория' };
const test4Result = filterCategoriesToProcess(mockCategories, test4Config);
console.log(`✅ Результат: ${test4Result.length} категорий`);
console.log(`📋 Первая категория: ${test4Result[0]?.name}`);

console.log('\n' + '='.repeat(80));

console.log('\n🎉 Все тесты завершены!');
console.log('\n💡 Примеры использования CLI:');
console.log('bun src/cli.ts start --skip-categories "Rotary Seals / Simmerring®"');
console.log('bun src/cli.ts start --start-from "Hydraulics / Rod Seals (Hydraulic)"');
console.log('bun src/cli.ts start --skip-categories "Rotary Seals / Simmerring®,Rotary Seals / Radiamatic®" --start-from "Rotary Seals / Axial Seals"');
console.log('bun src/cli.ts list-categories');
