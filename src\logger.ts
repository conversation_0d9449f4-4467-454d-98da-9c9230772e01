import * as fs from 'fs-extra';
import { ScrapingProgress } from './types';

export class ScrapingLogger {
  private logFile: string;
  private progress: ScrapingProgress;

  constructor(logFile: string) {
    this.logFile = logFile;
    this.progress = this.loadProgress();
  }

  private loadProgress(): ScrapingProgress {
    try {
      if (fs.existsSync(this.logFile)) {
        return fs.readJsonSync(this.logFile);
      }
    } catch (error) {
      console.warn('Failed to load progress log:', error);
    }

    // Default progress state
    return {
      startedAt: new Date().toISOString(),
      lastUpdatedAt: new Date().toISOString(),
      totalCategories: 0,
      scrapedCategories: 0,
      totalProducts: 0,
      scrapedProducts: 0,
      failedProducts: [],
      completedProducts: [],
      status: 'running'
    };
  }

  private saveProgress(): void {
    try {
      this.progress.lastUpdatedAt = new Date().toISOString();
      fs.writeJsonSync(this.logFile, this.progress, { spaces: 2 });
    } catch (error) {
      console.error('Failed to save progress log:', error);
    }
  }

  getProgress(): ScrapingProgress {
    return { ...this.progress };
  }

  updateTotalCounts(categories: number, products: number): void {
    this.progress.totalCategories = categories;
    this.progress.totalProducts = products;
    this.saveProgress();
  }

  markCategoryCompleted(): void {
    this.progress.scrapedCategories++;
    this.saveProgress();
  }

  markProductCompleted(productId: string): void {
    if (!this.progress.completedProducts.includes(productId)) {
      this.progress.completedProducts.push(productId);
      this.progress.scrapedProducts++;
      this.saveProgress();
    }
  }

  markProductFailed(productId: string): void {
    if (!this.progress.failedProducts.includes(productId)) {
      this.progress.failedProducts.push(productId);
      this.saveProgress();
    }
  }

  setCurrentCategory(category: string): void {
    this.progress.currentCategory = category;
    this.saveProgress();
  }

  setStatus(status: ScrapingProgress['status']): void {
    this.progress.status = status;
    this.saveProgress();
  }

  isProductCompleted(productId: string): boolean {
    return this.progress.completedProducts.includes(productId);
  }

  getCompletionStats(): { completed: number; total: number; percentage: number } {
    const completed = this.progress.scrapedProducts;
    const total = this.progress.totalProducts;
    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;
    
    return { completed, total, percentage };
  }

  logInfo(message: string): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] INFO: ${message}`;
    console.log(logMessage);
    this.writeToLogFile('INFO', message);
  }

  logError(message: string, error?: any): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ERROR: ${message}`;
    console.error(logMessage, error);
    this.writeToLogFile('ERROR', message, error);
  }

  logWarning(message: string): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] WARNING: ${message}`;
    console.warn(logMessage);
    this.writeToLogFile('WARNING', message);
  }

  logDebug(message: string): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] DEBUG: ${message}`;
    console.log(logMessage);
    this.writeToLogFile('DEBUG', message);
  }

  logSuccess(message: string): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] SUCCESS: ${message}`;
    console.log(`\x1b[32m${logMessage}\x1b[0m`); // Зеленый цвет
    this.writeToLogFile('SUCCESS', message);
  }

  private writeToLogFile(level: string, message: string, error?: any): void {
    try {
      const logEntry = {
        timestamp: new Date().toISOString(),
        level,
        message,
        error: error ? String(error) : undefined,
        category: this.progress.currentCategory
      };

      const logFilePath = this.logFile.replace('.json', '.log');
      const logLine = JSON.stringify(logEntry) + '\n';

      fs.appendFileSync(logFilePath, logLine);
    } catch (err) {
      console.error('Failed to write to log file:', err);
    }
  }

  reset(): void {
    this.progress = {
      startedAt: new Date().toISOString(),
      lastUpdatedAt: new Date().toISOString(),
      totalCategories: 0,
      scrapedCategories: 0,
      totalProducts: 0,
      scrapedProducts: 0,
      failedProducts: [],
      completedProducts: [],
      status: 'running'
    };
    this.saveProgress();
  }
}
