export interface ScraperConfig {
  baseUrl: string;
  outputDir: string;
  logFile: string;
  delay?: number;
  maxRetries?: number;
  skipCategories?: string[]; // Массив названий категорий для пропуска
  startFromCategory?: string; // Название категории, с которой начать скрапинг
}

export interface ProductInfo {
  fstItemNo: string;
  legacyItemNo?: string;
  successorItem?: string;
  variant?: string;
  netWeight?: string;
  packaging?: string;
  reach?: string;
  rohs?: string;
  brand?: string;
  material?: string;
  title: string;
  description: string;
  specifications: Record<string, string>;
  dimensions: Record<string, string>;
  images: string[];
  localImages: string[]; // Пути к локально сохраненным изображениям
  downloads: Array<{url: string, filename: string, type: string}>; // PDF файлы из секции Downloads
  localDownloads: string[]; // Пути к локально сохраненным PDF файлам
  category: string;
  subcategory: string;
  url: string;
  scrapedAt: string;
}

export interface ScrapingProgress {
  startedAt: string;
  lastUpdatedAt: string;
  totalCategories: number;
  scrapedCategories: number;
  totalProducts: number;
  scrapedProducts: number;
  failedProducts: string[];
  completedProducts: string[];
  currentCategory?: string;
  status: 'running' | 'completed' | 'paused' | 'error';
}

export interface CategoryInfo {
  name: string;
  url: string;
  subcategories: SubcategoryInfo[];
  totalProducts: number;
  scraped: boolean;
}

export interface SubcategoryInfo {
  name: string;
  url: string;
  description?: string;
  images: string[];
  products: ProductInfo[];
  totalPages: number;
  totalProducts: number;
  scrapedAt?: string;
}
