{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020", "DOM"], "outDir": "./dist", "rootDir": "./", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "sourceMap": false, "removeComments": true, "moduleResolution": "node"}, "include": ["**/*.ts"], "exclude": ["node_modules", "dist", "test-*.ts"]}