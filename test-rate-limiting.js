const { UnifiedFSTScraper } = require('./dist/src/unified-scraper.js');
const { ScrapingLogger } = require('./dist/src/logger.js');
const path = require('path');

async function testRateLimiting() {
  console.log('🧪 === ТЕСТ ОБРАБОТКИ ОГРАНИЧЕНИЙ СКОРОСТИ ===');
  
  const config = {
    baseUrl: 'https://products.fst.com/global/en',
    outputDir: path.join(__dirname, 'test-rate-limit'),
    logFile: path.join(__dirname, 'test-rate-limit.json'),
    delay: 3000, // Уменьшенная задержка для теста
    maxRetries: 2
  };

  const scraper = new UnifiedFSTScraper(config);
  
  try {
    console.log('⏳ Инициализация браузера...');
    await scraper.start();
    
  } catch (error) {
    console.log('❌ Ошибка:', error.message);
  } finally {
    console.log('✅ Тест завершен');
  }
}

testRateLimiting().catch(console.error);
