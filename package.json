{"name": "fst-scraper", "version": "1.0.0", "main": "dist/index.js", "private": true, "scripts": {"build": "tsc", "start": "npm run build && node dist/index.js start", "status": "npm run build && node dist/index.js status", "reset": "npm run build && node dist/index.js reset", "help": "npm run build && node dist/index.js help", "dev": "ts-node index.ts", "dev:start": "ts-node index.ts start", "dev:status": "ts-node index.ts status", "dev:reset": "ts-node index.ts reset"}, "devDependencies": {"@types/fs-extra": "^11.0.4", "@types/node": "^24.3.0", "ts-node": "^10.9.2", "typescript": "^5.9.2"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"cheerio": "^1.1.2", "fs-extra": "^11.3.0", "playwright": "^1.54.1"}}