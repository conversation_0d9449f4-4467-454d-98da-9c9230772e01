import { chromium, <PERSON>rowser, <PERSON> } from 'playwright';
import * as fs from 'fs-extra';
import * as path from 'path';
import { ScraperConfig, ProductInfo, CategoryInfo, SubcategoryInfo } from './types.js';
import { ScrapingLogger } from './logger.js';


const HEADLESS = false
export class UnifiedFSTScraper {
  private config: ScraperConfig;
  private logger: ScrapingLogger;
  private browser?: Browser;
  private page?: Page;
  private imagesDir: string;

  constructor(config: ScraperConfig) {
    this.config = {
      delay: 8000, // Увеличиваем задержку до 8 секунд для предотвращения 429 ошибок
      maxRetries: 5, // Увеличиваем количество попыток,
      ...config
    };
    this.logger = new ScrapingLogger(config.logFile);
    this.imagesDir = path.join(config.outputDir, 'images');

    // Создаем необходимые директории
    fs.ensureDirSync(config.outputDir);
    fs.ensureDirSync(this.imagesDir);
  }

  async start(): Promise<void> {
    try {
      this.logger.setStatus('running');
      this.logger.logInfo('🚀 Запуск единого FST скраппера...');

      // Диагностика системы
      await this.systemDiagnostics();

      await this.initBrowser();
      
      const categories = await this.getMainCategories();
      this.logger.updateTotalCounts(categories.length, 0);
      this.logger.logInfo(`📂 Найдено ${categories.length} основных категорий`);

      for (const category of categories) {
        await this.processCategory(category);
        await this.delay();
      }
      
      this.logger.setStatus('completed');
      this.logger.logSuccess('🎉 Скрапинг успешно завершен!');
      
    } catch (error) {
      this.logger.setStatus('error');
      this.logger.logError('💥 Критическая ошибка скрапинга', error);
      throw error;
    } finally {
      await this.closeBrowser();
    }
  }

  private async initBrowser(): Promise<void> {
    this.logger.logInfo('🌐 Инициализация браузера...');

    const maxRetries = 3;
    let attempt = 0;

    while (attempt < maxRetries) {
      try {
        attempt++;
        this.logger.logInfo(`🔄 Попытка ${attempt}/${maxRetries} запуска браузера...`);

        // Проверяем доступность Playwright
        this.logger.logDebug('🔍 Проверка доступности Playwright...');

        const launchOptions = {
          headless: HEADLESS,
          timeout: 60000, // Увеличиваем таймаут
          args: [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor'
          ]
        };

        this.logger.logDebug(`🚀 Запуск браузера с опциями: ${JSON.stringify(launchOptions)}`);

        try {
          this.browser = await chromium.launch(launchOptions);
          this.logger.logSuccess('✅ Браузер запущен');
        } catch (launchError) {
          this.logger.logWarning(`⚠️ Ошибка запуска с полными опциями: ${launchError}`);

          // Пробуем минимальные опции
          this.logger.logDebug('🔄 Попытка запуска с минимальными опциями...');
          const minimalOptions = {
            headless: HEADLESS,
            timeout: 30000,
            args: ['--no-sandbox', '--disable-dev-shm-usage']
          };

          this.browser = await chromium.launch(minimalOptions);
          this.logger.logSuccess('✅ Браузер запущен с минимальными опциями');
        }

        this.logger.logDebug('📄 Создание новой страницы...');
        this.page = await this.browser.newPage({
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        });
        this.logger.logSuccess('✅ Страница создана');

        // Пока отключаем блокировку запросов - просто игнорируем ошибки в логах
        this.logger.logDebug('� Отключаем блокировку запросов для стабильности...');
        this.logger.logSuccess('✅ Блокировка отключена');

        this.logger.logDebug('🖥️ Установка размера viewport...');
        await this.page.setViewportSize({ width: 1920, height: 1080 });
        this.page.setDefaultTimeout(60000);
        this.logger.logSuccess('✅ Viewport настроен');

        // Настройка обработчиков событий страницы
        this.logger.logDebug('🔧 Настройка обработчиков событий...');
        this.page.on('console', msg => {
          const text = msg.text();

          // Фильтруем неважные консольные сообщения
          if (text.includes('Permissions-Policy') ||
              text.includes('GTM-Support')) {
            // Пропускаем эти сообщения
          } else if (text.includes('429')) {
            this.logger.logWarning(`🚫 Console 429 error: ${text}`);
          } else {
            this.logger.logDebug(`🖥️ Console [${msg.type()}]: ${text}`);
          }
        });

        this.page.on('pageerror', error => {
          this.logger.logWarning(`⚠️ Page error: ${error.message}`);
        });

        // Обработка закрытия браузера
        this.browser.on('disconnected', () => {
          this.logger.logWarning('⚠️ Браузер неожиданно отключился');
        });

        this.page.on('requestfailed', request => {
          const url = request.url();
          const errorText = request.failure()?.errorText;

          // Игнорируем все ошибки запросов - просто логируем как debug
          if (url.includes('sentry.io') || url.includes('ingest.sentry.io')) {
            // Полностью игнорируем Sentry ошибки
          } else {
            this.logger.logDebug(`🔇 Request failed (игнорируем): ${url} - ${errorText}`);
          }
        });

        // Тестовый переход на страницу
        this.logger.logDebug(`🌐 Тестовый переход на: ${this.config.baseUrl}`);
        const startTime = Date.now();

        await this.page.goto(this.config.baseUrl, {
          waitUntil: 'domcontentloaded',
          timeout: 30000
        });

        const loadTime = Date.now() - startTime;
        this.logger.logSuccess(`✅ Тестовый переход успешен (${loadTime}ms)`);

        // Проверка что страница загрузилась
        const title = await this.page.title();
        this.logger.logDebug(`📄 Заголовок страницы: ${title}`);

        this.logger.logSuccess('🎉 Браузер успешно инициализирован и протестирован');
        return;

      } catch (error) {
        this.logger.logError(`❌ Ошибка попытки ${attempt}/${maxRetries}:`, error);
        this.logger.logError(`📋 Детали ошибки: ${error instanceof Error ? error.message : String(error)}`);
        this.logger.logError(`📋 Stack trace: ${error instanceof Error ? error.stack : 'N/A'}`);

        // Закрываем браузер если он был создан
        if (this.browser) {
          try {
            this.logger.logDebug('🔒 Закрытие поврежденного браузера...');
            await this.browser.close();
            this.browser = undefined;
            this.page = undefined;
            this.logger.logDebug('✅ Поврежденный браузер закрыт');
          } catch (closeError) {
            this.logger.logWarning(`⚠️ Ошибка закрытия браузера: ${closeError}`);
          }
        }

        if (attempt >= maxRetries) {
          this.logger.logError('💥 Все попытки инициализации браузера исчерпаны');
          throw new Error(`Не удалось инициализировать браузер после ${maxRetries} попыток. Последняя ошибка: ${error instanceof Error ? error.message : String(error)}`);
        }

        this.logger.logInfo(`⏳ Ожидание 5 секунд перед следующей попыткой...`);
        await this.delay(5000);
      }
    }
  }

  private async systemDiagnostics(): Promise<void> {
    this.logger.logInfo('🔍 Диагностика системы...');

    try {
      // Проверка Node.js версии
      this.logger.logDebug(`📋 Node.js версия: ${process.version}`);
      this.logger.logDebug(`📋 Платформа: ${process.platform} ${process.arch}`);
      this.logger.logDebug(`📋 Рабочая директория: ${process.cwd()}`);

      // Проверка памяти
      const memUsage = process.memoryUsage();
      this.logger.logDebug(`💾 Использование памяти: RSS=${Math.round(memUsage.rss / 1024 / 1024)}MB, Heap=${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);

      // Проверка доступности Playwright
      this.logger.logDebug('🎭 Проверка Playwright...');
      const playwrightVersion = require('playwright/package.json').version;
      this.logger.logDebug(`🎭 Playwright версия: ${playwrightVersion}`);

      // Проверка браузеров Playwright
      this.logger.logDebug('🌐 Проверка установленных браузеров...');

      this.logger.logSuccess('✅ Системная диагностика завершена');

    } catch (error) {
      this.logger.logWarning(`⚠️ Ошибка диагностики: ${error}`);
    }
  }

  private async closeBrowser(): Promise<void> {
    if (this.browser) {
      this.logger.logInfo('🔒 Закрытие браузера...');
      try {
        await this.browser.close();
        this.logger.logSuccess('✅ Браузер закрыт');
      } catch (error) {
        this.logger.logWarning(`⚠️ Ошибка закрытия браузера: ${error}`);
      }
    }
  }

  private async getMainCategories(): Promise<CategoryInfo[]> {
    if (!this.page) throw new Error('Browser not initialized');

    this.logger.logInfo('📋 Получение списка основных категорий...');
    
    const response = await this.page.goto(this.config.baseUrl, {
      waitUntil: 'domcontentloaded',
      timeout: 60000
    });

    // Проверяем на ошибки 429
    if (response && response.status() === 429) {
      this.logger.logWarning(`🚫 Получен статус 429 при загрузке главной страницы`);
      await this.handleRateLimit();
      // Повторяем попытку
      await this.page.goto(this.config.baseUrl, {
        waitUntil: 'domcontentloaded',
        timeout: 60000
      });
    }

    await this.smartDelay();

    const mainCategoryLinks = await this.page.$$eval('a[href*="/categories/"]', links => {
      return links.map(link => ({
        name: link.textContent?.trim() || '',
        url: (link as HTMLAnchorElement).href
      })).filter(cat => {
        const hasName = cat.name && cat.url;
        const isMainCategory = cat.url.includes('/categories/') && 
                              !cat.url.endsWith('/categories') &&
                              cat.name.length > 2 &&
                              (cat.url.match(/\/categories\/[^\/]+$/));
        return hasName && isMainCategory;
      });
    });

    const categories: CategoryInfo[] = [];
    
    for (const mainCategoryLink of mainCategoryLinks) {
      try {
        this.logger.logInfo(`📂 Обработка категории: ${mainCategoryLink.name}`);
        
        const response = await this.page.goto(mainCategoryLink.url, {
          waitUntil: 'domcontentloaded',
          timeout: 60000
        });

        // Проверяем на ошибки 429
        if (response && response.status() === 429) {
          this.logger.logWarning(`� Получен статус 429 для категории ${mainCategoryLink.name}`);
          await this.handleRateLimit();
          // Повторяем попытку
          await this.page.goto(mainCategoryLink.url, {
            waitUntil: 'domcontentloaded',
            timeout: 60000
          });
        }

        await this.smartDelay();
        
        const subcategoryLinks = await this.page.$$eval('.category-tile__link, a.cardTile', links => {
          return links.map(link => ({
            name: link.textContent?.trim() || '',
            url: (link as HTMLAnchorElement).href
          })).filter(subcat => {
            const hasName = subcat.name && subcat.url;
            const isSubcategory = subcat.url.includes('/categories/') && 
                                 subcat.name.length > 2;
            return hasName && isSubcategory;
          });
        });

        const subcategories: SubcategoryInfo[] = subcategoryLinks.map(link => ({
          name: link.name,
          url: link.url,
          images: [],
          products: [],
          totalPages: 0,
          totalProducts: 0
        }));

        categories.push({
          name: mainCategoryLink.name,
          url: mainCategoryLink.url,
          subcategories,
          totalProducts: 0,
          scraped: false
        });

        this.logger.logInfo(`   ✅ Найдено ${subcategories.length} подкатегорий в ${mainCategoryLink.name}`);
        
      } catch (error) {
        this.logger.logError(`❌ Ошибка обработки категории ${mainCategoryLink.name}`, error);
      }
    }

    this.logger.logSuccess(`✅ Получено ${categories.length} категорий`);
    return categories;
  }

  private async processCategory(category: CategoryInfo): Promise<void> {
    this.logger.setCurrentCategory(category.name);
    this.logger.logInfo(`🔄 Обработка категории: ${category.name}`);

    for (const subcategory of category.subcategories) {
      await this.processSubcategory(category, subcategory);
      await this.delay();
    }

    await this.saveCategoryData(category);
    category.scraped = true;
    this.logger.markCategoryCompleted();
    this.logger.logSuccess(`✅ Завершена категория: ${category.name} (${category.totalProducts} товаров)`);
  }

  private async processSubcategory(category: CategoryInfo, subcategory: SubcategoryInfo): Promise<void> {
    if (!this.page) throw new Error('Browser not initialized');

    this.logger.logInfo(`   🔄 Обработка подкатегории: ${subcategory.name}`);

    try {
      const response = await this.page.goto(subcategory.url, {
        waitUntil: 'domcontentloaded',
        timeout: 60000
      });

      // Проверяем на ошибки 429
      if (response && response.status() === 429) {
        this.logger.logWarning(`🚫 Получен статус 429 для подкатегории ${subcategory.name}`);
        await this.handleRateLimit();
        // Повторяем попытку
        await this.page.goto(subcategory.url, {
          waitUntil: 'domcontentloaded',
          timeout: 60000
        });
      }

      await this.smartDelay();

      // Получаем информацию о подкатегории
      const subcategoryInfo = await this.getSubcategoryInfo();
      subcategory.description = subcategoryInfo.description;
      subcategory.images = subcategoryInfo.images;
      
      // Сначала проверяем, есть ли на этой странице конечные подкатегории (например, BAUM, BAUMSL и т.д.)
      const finalSubcategories = await this.getFinalSubcategories();

      if (finalSubcategories.length > 0) {
        this.logger.logInfo(`      📂 Найдено ${finalSubcategories.length} конечных подкатегорий в ${subcategory.name}`);

        // Обрабатываем каждую конечную подкатегорию
        for (const finalSubcat of finalSubcategories) {
          await this.processFinalSubcategory(finalSubcat, category.name, subcategory.name);
          subcategory.totalProducts += finalSubcat.products.length;
        }
      } else {
        // Если конечных подкатегорий нет, пробуем искать товары прямо здесь
        this.logger.logInfo(`      📦 Поиск товаров напрямую в подкатегории ${subcategory.name}`);
        await this.scrapeAllProductsFromSubcategory(subcategory, category.name);
      }
      
      subcategory.scrapedAt = new Date().toISOString();
      category.totalProducts += subcategory.products.length;
      
      this.logger.logSuccess(`      ✅ ${subcategory.name}: ${subcategory.products.length} товаров с ${subcategory.totalPages} страниц`);
      
    } catch (error) {
      this.logger.logError(`      ❌ Ошибка обработки подкатегории ${subcategory.name}`, error);
    }
  }

  private async getSubcategoryInfo(): Promise<{description?: string, images: string[]}> {
    if (!this.page) throw new Error('Browser not initialized');

    return await this.page.evaluate(() => {
      const description = document.querySelector('meta[name="description"]')?.getAttribute('content') || '';
      
      const images: string[] = [];
      const imgElements = document.querySelectorAll('img');
      imgElements.forEach(img => {
        if (img.src &&
            !img.src.includes('data:') &&
            !img.src.includes('icon') &&
            !img.src.includes('logo') &&
            !img.src.includes('sprite')) {
          images.push(img.src);
        }
      });

      return {
        description,
        images: Array.from(new Set(images))
      };
    });
  }

  private async delay(ms: number = this.config.delay || 5000): Promise<void> {
    this.logger.logDebug(`⏳ Ожидание ${ms}ms...`);
    await new Promise(resolve => setTimeout(resolve, ms));
  }

  private async smartDelay(): Promise<void> {
    // Умная задержка с увеличением при ошибках 429
    const baseDelay = this.config.delay || 5000;
    const randomDelay = Math.random() * 2000; // Добавляем случайность 0-2 сек
    const totalDelay = baseDelay + randomDelay;

    this.logger.logDebug(`🧠 Умная задержка: ${Math.round(totalDelay)}ms`);
    await this.delay(totalDelay);
  }

  private async handleRateLimit(): Promise<void> {
    // Обработка ограничения скорости (429 ошибки)
    const rateLimitDelay = 60000; // 60 секунд при блокировке (увеличиваем)
    this.logger.logWarning(`🚫 Обнаружено ограничение скорости, ожидание ${rateLimitDelay/1000} секунд...`);
    await this.delay(rateLimitDelay);

    // Дополнительная случайная задержка для избежания синхронизации
    const randomDelay = Math.random() * 10000; // 0-10 секунд
    this.logger.logDebug(`⏳ Дополнительная случайная задержка: ${Math.round(randomDelay/1000)} секунд`);
    await this.delay(randomDelay);
  }

  private async getFinalSubcategories(): Promise<SubcategoryInfo[]> {
    if (!this.page) throw new Error('Browser not initialized');

    try {
      // Ищем ссылки на конечные подкатегории (например, BAUM, BAUMSL и т.д.)
      const finalSubcategories = await this.page.$$eval('a[href*="/categories/"]', links => {
        return links
          .map(link => ({
            name: (link as HTMLAnchorElement).textContent?.trim() || '',
            url: (link as HTMLAnchorElement).href,
            description: ''
          }))
          .filter(item =>
            item.name &&
            item.url &&
            item.url.includes('/categories/') &&
            // Фильтруем только те ссылки, которые ведут на конечные категории с товарами
            (item.name.length < 20 && !item.name.includes('Category') && !item.name.includes('Home'))
          );
      });

      // Убираем дубликаты
      const uniqueSubcategories = finalSubcategories.filter((item, index, self) =>
        index === self.findIndex(t => t.url === item.url)
      );

      return uniqueSubcategories.map(item => ({
        name: item.name,
        url: item.url,
        description: item.description,
        images: [],
        products: [],
        totalPages: 0,
        totalProducts: 0,
        scrapedAt: ''
      }));

    } catch (error) {
      this.logger.logError('❌ Ошибка получения конечных подкатегорий', error);
      return [];
    }
  }

  private async processFinalSubcategory(finalSubcat: SubcategoryInfo, categoryName: string, subcategoryName: string): Promise<void> {
    this.logger.logInfo(`        🔄 Обработка конечной подкатегории: ${finalSubcat.name}`);

    try {
      const response = await this.page.goto(finalSubcat.url, {
        waitUntil: 'domcontentloaded',
        timeout: 60000
      });

      // Проверяем на ошибки 429
      if (response && response.status() === 429) {
        this.logger.logWarning(`🚫 Получен статус 429 для конечной подкатегории ${finalSubcat.name}`);
        await this.handleRateLimit();
        // Повторяем попытку
        await this.page.goto(finalSubcat.url, {
          waitUntil: 'domcontentloaded',
          timeout: 60000
        });
      }

      await this.smartDelay();

      // Скрапим все продукты со всех страниц
      await this.scrapeAllProductsFromSubcategory(finalSubcat, categoryName);

      finalSubcat.scrapedAt = new Date().toISOString();

      this.logger.logSuccess(`        ✅ ${finalSubcat.name}: ${finalSubcat.products.length} товаров с ${finalSubcat.totalPages} страниц`);

    } catch (error) {
      this.logger.logError(`        ❌ Ошибка обработки конечной подкатегории ${finalSubcat.name}`, error);
    }
  }

  private async scrapeAllProductsFromSubcategory(subcategory: SubcategoryInfo, categoryName: string): Promise<void> {
    if (!this.page) throw new Error('Browser not initialized');

    let currentPage = 1;
    let hasNextPage = true;
    const productsPerPage = 26;

    while (hasNextPage) {
      const pageUrl = `${subcategory.url}?page=${currentPage}&size=${productsPerPage}`;
      this.logger.logDebug(`        📄 Обработка страницы ${currentPage}: ${pageUrl}`);

      try {
        const response = await this.page.goto(pageUrl, {
          waitUntil: 'domcontentloaded',
          timeout: 30000
        });

        // Проверяем статус ответа на ошибки 429
        if (response && response.status() === 429) {
          this.logger.logWarning(`🚫 Получен статус 429 для страницы ${currentPage}`);
          await this.handleRateLimit();
          continue; // Повторяем ту же страницу
        }

        await this.smartDelay();

        // Прокрутка для загрузки контента
        await this.page.evaluate(() => {
          window.scrollTo(0, document.body.scrollHeight);
        });
        await this.page.waitForTimeout(3000);

        await this.page.evaluate(() => {
          window.scrollTo(0, 0);
        });
        await this.page.waitForTimeout(2000);

        // Ждем загрузки таблицы с товарами
        this.logger.logDebug(`        🔍 Ожидание загрузки таблицы товаров на странице ${currentPage}...`);

        try {
          // Ждем появления таблицы с товарами
          await this.page.waitForSelector('div[role="grid"]', { timeout: 30000 });
          this.logger.logDebug(`        ✅ Таблица товаров загружена`);
        } catch (error) {
          this.logger.logWarning(`        ⚠️ Таблица товаров не загрузилась: ${error}`);
        }

        // Дополнительная задержка для полной загрузки
        await this.page.waitForTimeout(3000);

        // Ищем ссылки на товары в таблице
        const productUrls = await this.page.$$eval('div[role="grid"] a[href*="/products/"]', links => {
          return Array.from(new Set(
            links.map(link => (link as HTMLAnchorElement).href)
              .filter(url => url && url.includes('/products/') && !url.includes('undefined'))
          ));
        });

        this.logger.logDebug(`        📋 Найдено ${productUrls.length} ссылок на товары в таблице`);

        if (productUrls.length === 0) {
          this.logger.logDebug(`        📄 Страница ${currentPage} пуста, завершаем обработку`);

          // Логируем содержимое страницы для отладки
          const pageContent = await this.page.content();
          this.logger.logDebug(`        📋 Размер HTML страницы: ${pageContent.length} символов`);

          hasNextPage = false;
          break;
        }

        this.logger.logDebug(`        📦 Найдено ${productUrls.length} товаров на странице ${currentPage}`);

        for (const productUrl of productUrls) {
          try {
            const product = await this.scrapeProduct(productUrl, categoryName, subcategory.name);
            if (product) {
              subcategory.products.push(product);
              this.logger.markProductCompleted(product.fstItemNo);
            }
          } catch (error) {
            this.logger.logError(`          ❌ Ошибка обработки товара ${productUrl}`, error);
          }

          await this.delay(1000);
        }

        subcategory.totalPages = currentPage;

        // Проверяем есть ли следующая страница и переходим на неё
        try {
          // Ищем кнопку "Go to the next page"
          const nextButton = await this.page.$('button[aria-label*="next page"], button:has-text("Go to the next page")');
          if (nextButton && await nextButton.isEnabled()) {
            this.logger.logDebug(`        ➡️ Переход на страницу ${currentPage + 1}...`);
            await nextButton.click();
            await this.page.waitForTimeout(5000); // Ждем загрузки новой страницы
            currentPage++;
          } else {
            this.logger.logDebug(`        📄 Достигнута последняя страница (кнопка Next недоступна)`);
            hasNextPage = false;
          }
        } catch (error) {
          this.logger.logDebug(`        ❌ Ошибка при переходе на следующую страницу: ${error}`);
          hasNextPage = false;
        }

      } catch (error) {
        this.logger.logError(`        ❌ Ошибка обработки страницы ${currentPage}`, error);
        hasNextPage = false;
      }
    }
  }

  private async scrapeProduct(productUrl: string, categoryName: string, subcategoryName: string, retryCount: number = 0): Promise<ProductInfo | null> {
    if (!this.page) throw new Error('Browser not initialized');

    try {
      const response = await this.page.goto(productUrl, {
        waitUntil: 'domcontentloaded',
        timeout: 30000
      });

      // Проверяем статус ответа на ошибки 429
      if (response && response.status() === 429) {
        this.logger.logWarning(`🚫 Получен статус 429 для товара ${productUrl}`);
        await this.handleRateLimit();

        if (retryCount < this.config.maxRetries) {
          return this.scrapeProduct(productUrl, categoryName, subcategoryName, retryCount + 1);
        } else {
          this.logger.logError(`❌ Превышено количество попыток для товара ${productUrl}`);
          return null;
        }
      }

      await this.smartDelay();

      const productData = await this.page.evaluate(() => {
        const fstItemNo = window.location.pathname.split('/').pop() || '';
        const title = document.querySelector('h1')?.textContent?.trim() || document.title;
        const description = document.querySelector('meta[name="description"]')?.getAttribute('content') || '';

        const specifications: Record<string, string> = {};
        const dimensions: Record<string, string> = {};

        // Извлекаем характеристики из основной области продукта
        // Убираем неправильный селектор и используем только XPath

        // Ищем все текстовые блоки с характеристиками
        const allDivs = document.querySelectorAll('div');
        const specPatterns = [
          { key: 'Legacy Item No', selector: 'div:contains("434416")' },
          { key: 'Packaging', text: 'Industry Pack' },
          { key: 'Net weight', text: 'GRM' },
          { key: 'Packaging quantity', text: 'PCE' },
          { key: 'REACH', text: 'Material not affected by REACH' },
          { key: 'ROHS', text: 'COMPLIANT' },
          { key: 'Brand', text: 'Freudenberg' },
          { key: 'Material', text: 'FKM' }
        ];

        // Проходим по всем div элементам и ищем характеристики
        allDivs.forEach(div => {
          const text = div.textContent?.trim();
          if (!text) return;

          // Проверяем паттерны
          if (text.includes('GRM') && text.match(/[\d.]+\s*GRM/)) {
            specifications['Net weight'] = text;
          } else if (text.includes('PCE') && text.match(/\d+\s*PCE/)) {
            specifications['Packaging'] = text;
          } else if (text === 'Material not affected by REACH') {
            specifications['REACH'] = text;
          } else if (text === 'COMPLIANT') {
            specifications['ROHS'] = text;
          } else if (text === 'Freudenberg') {
            specifications['Brand'] = text;
          } else if (text.includes('FKM')) {
            specifications['Material'] = text;
          } else if (text.includes('434416')) {
            specifications['Legacy Item No'] = text;
          }
        });

        // Извлекаем размеры - правильный способ
        const allElements = document.querySelectorAll('*');
        allElements.forEach(el => {
          const text = el.textContent?.trim();
          if (!text) return;

          // Проверяем точные совпадения размеров
          if (text === '30 mm') {
            // Ищем соседние элементы для контекста
            const siblings = Array.from(el.parentElement?.children || []);
            const hasInnerDiameter = siblings.some(s => s.textContent?.includes('Inner diameter'));
            if (hasInnerDiameter) {
              dimensions['Inner diameter (d1)'] = text;
            }
          } else if (text === '47 mm') {
            const siblings = Array.from(el.parentElement?.children || []);
            const hasOuterDiameter = siblings.some(s => s.textContent?.includes('Outer diameter'));
            if (hasOuterDiameter) {
              dimensions['Outer diameter (d2)'] = text;
            }
          } else if (text === '7 mm') {
            const siblings = Array.from(el.parentElement?.children || []);
            const hasSealWidth = siblings.some(s => s.textContent?.includes('Seal width'));
            if (hasSealWidth) {
              dimensions['Seal width (b)'] = text;
            }
          }
        });

        // Дополнительный поиск характеристик в других местах
        const allText = document.body.textContent || '';
        const patterns = [
          { key: 'Net weight', regex: /Net weight[:\s]+([\d.,]+\s*(?:g|kg|lb|oz))/i },
          { key: 'Packaging', regex: /Packaging[:\s]+([^.\n]+)/i },
          { key: 'Material', regex: /Material[:\s]+([^.\n]+)/i },
          { key: 'Brand', regex: /Brand[:\s]+([^.\n]+)/i },
          { key: 'REACH', regex: /REACH[:\s]+([^.\n]+)/i },
          { key: 'ROHS', regex: /ROHS[:\s]+([^.\n]+)/i }
        ];

        patterns.forEach(pattern => {
          const match = allText.match(pattern.regex);
          if (match && match[1] && !specifications[pattern.key]) {
            specifications[pattern.key] = match[1].trim();
          }
        });

        // Извлекаем изображения
        const images: string[] = [];
        const imgElements = document.querySelectorAll('img');
        imgElements.forEach(img => {
          if (img.src &&
              !img.src.includes('data:') &&
              !img.src.includes('icon') &&
              !img.src.includes('logo') &&
              !img.src.includes('sprite')) {
            images.push(img.src);
          }
        });

        // Извлекаем PDF файлы из секции Downloads
        const downloads: Array<{url: string, filename: string, type: string}> = [];
        const pdfLinks = document.querySelectorAll('a[href*=".pdf"]');

        pdfLinks.forEach(link => {
          const href = link.getAttribute('href');
          const text = link.textContent?.trim();

          if (href && text) {
            const fullUrl = href.startsWith('http') ? href : `https://products.fst.com${href}`;

            // Проверяем что такой ссылки еще нет
            if (!downloads.some(d => d.url === fullUrl)) {
              downloads.push({
                url: fullUrl,
                filename: text,
                type: 'pdf'
              });
            }
          }
        });

        return {
          fstItemNo,
          title,
          description,
          specifications,
          dimensions,
          images: Array.from(new Set(images)),
          downloads,
          url: window.location.href
        };
      });

      // Скачиваем изображения локально
      const localImages = await this.downloadProductImages(productData.images, productData.fstItemNo);

      // Скачиваем PDF файлы локально
      const localDownloads = await this.downloadProductPDFs(productData.downloads, productData.fstItemNo);

      const product: ProductInfo = {
        ...productData,
        localImages,
        localDownloads,
        category: categoryName,
        subcategory: subcategoryName,
        scrapedAt: new Date().toISOString()
      };

      this.logger.logDebug(`          ✅ Товар обработан: ${product.fstItemNo} (${product.images.length} изображений, ${product.downloads.length} PDF файлов)`);
      return product;

    } catch (error) {
      this.logger.logError(`          ❌ Ошибка скрапинга товара ${productUrl}`, error);
      return null;
    }
  }

  private async downloadProductImages(imageUrls: string[], productId: string): Promise<string[]> {
    const localPaths: string[] = [];

    if (!imageUrls || imageUrls.length === 0) {
      return localPaths;
    }

    const productImagesDir = path.join(this.imagesDir, productId);
    fs.ensureDirSync(productImagesDir);

    for (let i = 0; i < imageUrls.length; i++) {
      try {
        const imageUrl = imageUrls[i];
        const response = await fetch(imageUrl);

        if (response.ok) {
          const buffer = await response.arrayBuffer();
          const extension = path.extname(new URL(imageUrl).pathname) || '.jpg';
          const filename = `image_${i + 1}${extension}`;
          const localPath = path.join(productImagesDir, filename);

          await fs.writeFile(localPath, Buffer.from(buffer));
          localPaths.push(path.relative(this.config.outputDir, localPath));

          this.logger.logDebug(`            📷 Изображение сохранено: ${filename}`);
        }
      } catch (error) {
        this.logger.logWarning(`            ⚠️ Не удалось скачать изображение ${imageUrls[i]}: ${error}`);
      }
    }

    return localPaths;
  }

  private async downloadProductPDFs(downloads: Array<{url: string, filename: string, type: string}>, productId: string): Promise<string[]> {
    const localPaths: string[] = [];

    if (!downloads || downloads.length === 0) {
      return localPaths;
    }

    const productDownloadsDir = path.join(this.imagesDir, productId, 'downloads');
    fs.ensureDirSync(productDownloadsDir);

    for (let i = 0; i < downloads.length; i++) {
      try {
        const download = downloads[i];
        this.logger.logDebug(`            📄 Скачивание PDF: ${download.filename}`);

        const response = await fetch(download.url);

        if (response.ok) {
          const buffer = await response.arrayBuffer();

          // Создаем безопасное имя файла
          const safeFilename = download.filename
            .replace(/[^a-zA-Z0-9\s\-_.()]/g, '_')
            .replace(/\s+/g, '_')
            .toLowerCase();

          const filename = safeFilename.endsWith('.pdf') ? safeFilename : `${safeFilename}.pdf`;
          const localPath = path.join(productDownloadsDir, filename);

          await fs.writeFile(localPath, Buffer.from(buffer));
          localPaths.push(path.relative(this.config.outputDir, localPath));

          this.logger.logDebug(`            ✅ PDF сохранен: ${filename}`);
        } else {
          this.logger.logWarning(`            ⚠️ Не удалось скачать PDF ${download.url}: HTTP ${response.status}`);
        }
      } catch (error) {
        this.logger.logWarning(`            ⚠️ Ошибка скачивания PDF ${downloads[i].url}: ${error}`);
      }
    }

    return localPaths;
  }

  private async saveCategoryData(category: CategoryInfo): Promise<void> {
    try {
      const filename = `${category.name.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase()}.json`;
      const filepath = path.join(this.config.outputDir, filename);

      await fs.writeJson(filepath, category, { spaces: 2 });
      this.logger.logInfo(`💾 Данные категории сохранены: ${filepath}`);

    } catch (error) {
      this.logger.logError(`❌ Ошибка сохранения данных категории ${category.name}`, error);
    }
  }
}
