// Тестовый скрипт для проверки функции пропуска категорий
import { browser_navigate_Playwright, browser_evaluate_Playwright } from './browser-tools.js';

async function testCategoryExtraction() {
  console.log('🧪 Тестирование извлечения категорий...');
  
  try {
    // Переходим на страницу категорий
    await browser_navigate_Playwright('https://products.fst.com/global/en/categories');
    
    // Извлекаем список основных категорий
    const categories = await browser_evaluate_Playwright(`
      () => {
        const categoryElements = document.querySelectorAll('h2');
        const categories = [];
        
        categoryElements.forEach(h2 => {
          const categoryName = h2.textContent?.trim();
          if (categoryName && categoryName.includes('/')) {
            // Ищем ссылку "Show all" рядом с заголовком
            const showAllLink = h2.parentElement?.querySelector('a[href*="/categories/"]');
            if (showAllLink) {
              categories.push({
                name: categoryName,
                url: showAllLink.href,
                subcategories: []
              });
            }
          }
        });
        
        return categories;
      }
    `);
    
    console.log('📂 Найденные категории:');
    categories.forEach((category, index) => {
      console.log(`${index + 1}. ${category.name}`);
      console.log(`   URL: ${category.url}`);
    });
    
    console.log(`\n📋 Всего найдено ${categories.length} категорий`);
    
    // Тестируем функцию фильтрации
    console.log('\n🧪 Тестирование фильтрации категорий...');
    
    // Тест 1: Пропуск первой категории
    const skipCategories = ['Rotary Seals / Simmerring®'];
    const filteredCategories1 = filterCategoriesToProcess(categories, { skipCategories });
    console.log(`\n✅ Тест 1 - Пропуск "${skipCategories[0]}": ${filteredCategories1.length} категорий (было ${categories.length})`);
    
    // Тест 2: Начать с определенной категории
    const startFromCategory = 'Hydraulics / Rod Seals (Hydraulic)';
    const filteredCategories2 = filterCategoriesToProcess(categories, { startFromCategory });
    console.log(`\n✅ Тест 2 - Начать с "${startFromCategory}": ${filteredCategories2.length} категорий`);
    
    // Тест 3: Комбинация пропуска и начала
    const filteredCategories3 = filterCategoriesToProcess(categories, { 
      skipCategories: ['Rotary Seals / Simmerring®', 'Rotary Seals / Radiamatic®'],
      startFromCategory: 'Rotary Seals / Radial Shaft Seals'
    });
    console.log(`\n✅ Тест 3 - Комбинация: ${filteredCategories3.length} категорий`);
    
    return categories;
    
  } catch (error) {
    console.error('❌ Ошибка тестирования:', error);
    throw error;
  }
}

// Функция фильтрации категорий (копия из основного кода)
function filterCategoriesToProcess(categories, config) {
  let filteredCategories = [...categories];

  // Пропускаем указанные категории
  if (config.skipCategories && config.skipCategories.length > 0) {
    const skipSet = new Set(config.skipCategories.map(name => name.toLowerCase()));
    filteredCategories = filteredCategories.filter(category => {
      const shouldSkip = skipSet.has(category.name.toLowerCase());
      if (shouldSkip) {
        console.log(`⏭️ Пропускаем категорию: ${category.name}`);
      }
      return !shouldSkip;
    });
  }

  // Начинаем с указанной категории
  if (config.startFromCategory) {
    const startFromIndex = filteredCategories.findIndex(
      category => category.name.toLowerCase() === config.startFromCategory.toLowerCase()
    );
    
    if (startFromIndex >= 0) {
      filteredCategories = filteredCategories.slice(startFromIndex);
      console.log(`🎯 Начинаем с категории: ${config.startFromCategory} (пропущено ${startFromIndex} категорий)`);
    } else {
      console.log(`⚠️ Категория "${config.startFromCategory}" не найдена, начинаем с первой доступной`);
    }
  }

  return filteredCategories;
}

// Запускаем тест
testCategoryExtraction()
  .then(categories => {
    console.log('\n🎉 Тестирование завершено успешно!');
    console.log('\n💡 Примеры использования:');
    console.log('bun src/cli.ts start --skip-categories "Rotary Seals / Simmerring®"');
    console.log('bun src/cli.ts start --start-from "Hydraulics / Rod Seals (Hydraulic)"');
    console.log('bun src/cli.ts start --skip-categories "Rotary Seals / Simmerring®,Rotary Seals / Radiamatic®" --start-from "Rotary Seals / Radial Shaft Seals"');
  })
  .catch(error => {
    console.error('💥 Тестирование не удалось:', error);
    process.exit(1);
  });
