# Пропуск категорий в FST Скраппере

## Обзор

Реализована возможность пропускать корневые (верхние) категории и начинать скрапинг с определенной категории. Это полезно когда:

- Одна или несколько категорий уже завершены
- Нужно продолжить скрапинг с определенного места
- Требуется скрапить только определенные категории

## Новые возможности

### 1. Пропуск категорий (`--skip-categories`)

Позволяет пропустить одну или несколько категорий:

```bash
# Пропустить одну категорию
bun src/cli.ts start --skip-categories "Rotary Seals / Simmerring®"

# Пропустить несколько категорий (через запятую)
bun src/cli.ts start --skip-categories "Rotary Seals / Simmerring®,Rotary Seals / Radiamatic®"
```

### 2. Начать с определенной категории (`--start-from`)

Начинает скрапинг с указанной категории (все предыдущие пропускаются):

```bash
# Начать с категории "Hydraulics / Rod Seals (Hydraulic)"
bun src/cli.ts start --start-from "Hydraulics / Rod Seals (Hydraulic)"
```

### 3. Комбинация параметров

Можно комбинировать оба параметра:

```bash
# Пропустить определенные категории И начать с конкретной
bun src/cli.ts start --skip-categories "Rotary Seals / Simmerring®,Rotary Seals / Radiamatic®" --start-from "Rotary Seals / Axial Seals"
```

### 4. Просмотр доступных категорий

Новая команда для просмотра всех доступных категорий:

```bash
bun src/cli.ts list-categories
```

## Примеры использования

### Сценарий 1: Продолжить после завершенной категории

Если категория "Rotary Seals / Simmerring®" уже завершена:

```bash
bun src/cli.ts start --skip-categories "Rotary Seals / Simmerring®"
```

### Сценарий 2: Начать с определенной категории

Если нужно начать скрапинг с категории "Hydraulics":

```bash
bun src/cli.ts start --start-from "Hydraulics / Rod Seals (Hydraulic)"
```

### Сценарий 3: Пропустить несколько завершенных категорий

Если несколько категорий уже завершены:

```bash
bun src/cli.ts start --skip-categories "Rotary Seals / Simmerring®,Rotary Seals / Radiamatic®,Rotary Seals / Radial Shaft Seals"
```

## Технические детали

### Изменения в коде

1. **Обновлен интерфейс `ScraperConfig`** в `src/types.ts`:
   - Добавлен `skipCategories?: string[]`
   - Добавлен `startFromCategory?: string`

2. **Добавлен метод `filterCategoriesToProcess()`** в `src/unified-scraper.ts`:
   - Фильтрует категории согласно настройкам
   - Логирует пропущенные категории

3. **Обновлен CLI** в `src/cli.ts`:
   - Парсинг новых аргументов командной строки
   - Новая команда `list-categories`
   - Обновленная справка

### Логика фильтрации

1. **Пропуск категорий**: Удаляет указанные категории из списка (регистронезависимо)
2. **Начать с категории**: Обрезает список, оставляя только категории начиная с указанной
3. **Порядок выполнения**: Сначала пропуск, затем обрезка по начальной категории

### Логирование

Скраппер логирует:
- Количество найденных категорий
- Количество категорий к обработке
- Количество пропущенных категорий
- Какие именно категории пропущены

## Тестирование

Запустите тестовый скрипт для проверки логики фильтрации:

```bash
node test-category-filtering.js
```

## Безопасность

- Названия категорий сравниваются регистронезависимо
- Если указанная категория для начала не найдена, скрапинг начинается с первой доступной
- Все изменения обратно совместимы - старые команды работают как прежде

## Примеры команд

```bash
# Просмотр всех категорий
bun src/cli.ts list-categories

# Обычный запуск (без изменений)
bun src/cli.ts start

# Пропустить завершенную категорию
bun src/cli.ts start --skip-categories "Rotary Seals / Simmerring®"

# Начать с определенной категории
bun src/cli.ts start --start-from "Static Seals / O-Rings"

# Комбинированный подход
bun src/cli.ts start --skip-categories "Rotary Seals / Simmerring®" --start-from "Hydraulics / Rod Seals (Hydraulic)"

# Проверка статуса
bun src/cli.ts status

# Сброс прогресса
bun src/cli.ts reset
```
