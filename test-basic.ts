#!/usr/bin/env bun

/**
 * 🧪 Базовый тест скраппера
 * 
 * Проверяет основные компоненты без запуска браузера
 */

import { ScrapingLogger } from './src/logger';
import { UnifiedFSTScraper } from './src/unified-scraper';
import * as path from 'path';
import * as fs from 'fs-extra';

async function testBasicFunctionality() {
  console.log('🧪 Тестирование базовой функциональности...');
  
  try {
    // Тест логгера
    console.log('📝 Тестируем логгер...');
    const testLogFile = path.join(__dirname, 'test-log.json');
    const logger = new ScrapingLogger(testLogFile);
    
    logger.logInfo('Тестовое сообщение');
    logger.logWarning('Тестовое предупреждение');
    logger.logSuccess('Тестовый успех');
    
    const progress = logger.getProgress();
    console.log(`   ✅ Логгер работает. Статус: ${progress.status}`);
    
    // Тест создания скраппера
    console.log('🚀 Тестируем создание скраппера...');
    const scraper = new UnifiedFSTScraper({
      baseUrl: 'https://products.fst.com/global/en',
      outputDir: path.join(__dirname, 'test-output'),
      logFile: testLogFile,
      delay: 1000,
      maxRetries: 2
    });
    
    console.log('   ✅ Скраппер создан успешно');
    
    // Тест создания директорий
    console.log('📁 Тестируем создание директорий...');
    const testOutputDir = path.join(__dirname, 'test-output');
    const testImagesDir = path.join(testOutputDir, 'images');
    
    fs.ensureDirSync(testOutputDir);
    fs.ensureDirSync(testImagesDir);
    
    if (fs.existsSync(testOutputDir) && fs.existsSync(testImagesDir)) {
      console.log('   ✅ Директории созданы успешно');
    } else {
      console.log('   ❌ Ошибка создания директорий');
    }
    
    // Тест сохранения JSON
    console.log('💾 Тестируем сохранение данных...');
    const testData = {
      name: 'Test Category',
      url: 'https://test.com',
      subcategories: [],
      totalProducts: 0,
      scraped: false,
      testTimestamp: new Date().toISOString()
    };
    
    const testDataPath = path.join(testOutputDir, 'test-category.json');
    await fs.writeJson(testDataPath, testData, { spaces: 2 });
    
    if (fs.existsSync(testDataPath)) {
      console.log('   ✅ Данные сохранены успешно');
    } else {
      console.log('   ❌ Ошибка сохранения данных');
    }
    
    // Очистка тестовых файлов
    console.log('🧹 Очищаем тестовые файлы...');
    if (fs.existsSync(testLogFile)) {
      fs.removeSync(testLogFile);
    }
    if (fs.existsSync(testOutputDir)) {
      fs.removeSync(testOutputDir);
    }
    
    console.log('🎉 Все базовые тесты прошли успешно!');
    console.log('');
    console.log('✅ Готово к использованию:');
    console.log('   bun index.ts start    - Запустить скрапинг');
    console.log('   bun index.ts status   - Проверить статус');
    console.log('   bun index.ts reset    - Сбросить прогресс');
    
  } catch (error) {
    console.error('❌ Ошибка базового теста:', error);
    process.exit(1);
  }
}

testBasicFunctionality();
