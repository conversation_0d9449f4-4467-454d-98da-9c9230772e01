#!/usr/bin/env node
import { UnifiedFSTScraper } from './unified-scraper.js';
import { ScrapingLogger } from './logger.js';
import * as path from 'path';
import * as fs from 'fs-extra';

const args = process.argv.slice(2);
const command = args[0];

// Парсим аргументы командной строки
function parseArgs(args: string[]): {
  command: string;
  skipCategories?: string[];
  startFromCategory?: string;
} {
  const result: any = { command: args[0] };

  for (let i = 1; i < args.length; i++) {
    const arg = args[i];

    if (arg === '--skip-categories' && i + 1 < args.length) {
      result.skipCategories = args[i + 1].split(',').map(s => s.trim());
      i++; // Пропускаем следующий аргумент
    } else if (arg === '--start-from' && i + 1 < args.length) {
      result.startFromCategory = args[i + 1].trim();
      i++; // Пропускаем следующий аргумент
    }
  }

  return result;
}

const parsedArgs = parseArgs(args);

async function main() {
  const rootDir = path.join(__dirname, '..');
  const outputDirDefault = path.join(rootDir, 'data');
  const logFileDefault = path.join(rootDir, 'scrape-log.json');

  let externalConfig: Partial<{
    baseUrl: string;
    delay: number;
    maxRetries: number;
    headless: boolean;
    userAgent: string;
    timeout: number;
    concurrency: number;
    downloadImages: boolean;
  }> = {};

  const configPath = path.join(rootDir, 'config.json');
  if (fs.existsSync(configPath)) {
    try {
      externalConfig = await fs.readJSON(configPath);
    } catch (e) {
      console.warn('Failed to read config.json, using defaults');
    }
  }

  const scraper = new UnifiedFSTScraper({
    baseUrl: externalConfig.baseUrl || 'https://products.fst.com/global/en',
    outputDir: outputDirDefault,
    logFile: logFileDefault,
    delay: externalConfig.delay ?? 1000,
    maxRetries: externalConfig.maxRetries ?? 3,
    skipCategories: parsedArgs.skipCategories,
    startFromCategory: parsedArgs.startFromCategory
  });

  const logger = new ScrapingLogger(logFileDefault);

  switch (parsedArgs.command) {
    case 'start':
      console.log('Starting scraper...');
      await scraper.start();
      break;
      
    case 'reset':
      console.log('Resetting scraper progress...');
      logger.reset();
      console.log('✅ Progress reset successfully');
      break;

    case 'status':
      const progress = logger.getProgress();
      console.log('=== Scraping Status ===');
      console.log(`Status: ${progress.status}`);
      console.log(`Started: ${progress.startedAt}`);
      console.log(`Last Updated: ${progress.lastUpdatedAt}`);
      console.log(`Categories: ${progress.scrapedCategories}/${progress.totalCategories}`);
      console.log(`Products: ${progress.scrapedProducts}/${progress.totalProducts}`);
      if (progress.currentCategory) {
        console.log(`Current Category: ${progress.currentCategory}`);
      }
      if (progress.failedProducts.length > 0) {
        console.log(`Failed Products: ${progress.failedProducts.length}`);
      }
      break;

    case 'list-categories':
      console.log('📂 Получение списка категорий...');
      try {
        const tempScraper = new UnifiedFSTScraper({
          baseUrl: externalConfig.baseUrl || 'https://products.fst.com/global/en',
          outputDir: outputDirDefault,
          logFile: logFileDefault,
          delay: externalConfig.delay ?? 1000,
          maxRetries: externalConfig.maxRetries ?? 3
        });

        // Временно инициализируем браузер для получения категорий
        await tempScraper.initBrowser();
        const categories = await tempScraper.getMainCategories();
        await tempScraper.closeBrowser();

        console.log('\n=== Доступные категории ===');
        categories.forEach((category, index) => {
          console.log(`${index + 1}. ${category.name}`);
          console.log(`   URL: ${category.url}`);
          console.log(`   Подкатегорий: ${category.subcategories.length}`);
          console.log('');
        });

        console.log(`\n📋 Всего найдено ${categories.length} категорий`);
        console.log('\n💡 Для пропуска категории используйте:');
        console.log('   bun src/cli.ts start --skip-categories "Название категории"');
        console.log('\n💡 Для начала с определенной категории используйте:');
        console.log('   bun src/cli.ts start --start-from "Название категории"');

      } catch (error) {
        console.error('❌ Ошибка получения списка категорий:', error);
      }
      break;
      
    default:
      console.log(`
🚀 Единый FST Скраппер CLI

Использование:
  bun src/cli.ts [команда] [опции]

Команды:
  start           - Запустить или продолжить скрапинг
  reset           - Сбросить прогресс и начать заново
  status          - Показать текущий прогресс скрапинга
  list-categories - Показать список всех доступных категорий

Опции для команды start:
  --skip-categories "Cat1,Cat2"  - Пропустить указанные категории (через запятую)
  --start-from "CategoryName"    - Начать скрапинг с указанной категории

Примеры:
  bun src/cli.ts list-categories
  bun src/cli.ts start
  bun src/cli.ts start --skip-categories "Rotary Seals / Simmerring®"
  bun src/cli.ts start --start-from "O-Rings"
  bun src/cli.ts start --skip-categories "Cat1,Cat2" --start-from "Cat3"
  bun src/cli.ts status
  bun src/cli.ts reset

Особенности:
  ✅ Подробное логирование процесса
  ✅ Локальное сохранение изображений
  ✅ Возможность возобновления после прерывания
  ✅ Детальная статистика прогресса
  ✅ Пропуск и выбор категорий для скрапинга
      `);
      break;
  }
}

main().catch(console.error);
