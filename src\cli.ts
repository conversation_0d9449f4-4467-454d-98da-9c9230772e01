#!/usr/bin/env node
import { UnifiedFSTScraper } from './unified-scraper.js';
import { ScrapingLogger } from './logger.js';
import * as path from 'path';
import * as fs from 'fs-extra';

const args = process.argv.slice(2);
const command = args[0];

async function main() {
  const rootDir = path.join(__dirname, '..');
  const outputDirDefault = path.join(rootDir, 'data');
  const logFileDefault = path.join(rootDir, 'scrape-log.json');

  let externalConfig: Partial<{
    baseUrl: string;
    delay: number;
    maxRetries: number;
    headless: boolean;
    userAgent: string;
    timeout: number;
    concurrency: number;
    downloadImages: boolean;
  }> = {};

  const configPath = path.join(rootDir, 'config.json');
  if (fs.existsSync(configPath)) {
    try {
      externalConfig = await fs.readJSON(configPath);
    } catch (e) {
      console.warn('Failed to read config.json, using defaults');
    }
  }

  const scraper = new UnifiedFSTScraper({
    baseUrl: externalConfig.baseUrl || 'https://products.fst.com/global/en',
    outputDir: outputDirDefault,
    logFile: logFileDefault,
    delay: externalConfig.delay ?? 1000,
    maxRetries: externalConfig.maxRetries ?? 3
  });

  const logger = new ScrapingLogger(logFileDefault);

  switch (command) {
    case 'start':
      console.log('Starting scraper...');
      await scraper.start();
      break;
      
    case 'reset':
      console.log('Resetting scraper progress...');
      logger.reset();
      console.log('✅ Progress reset successfully');
      break;

    case 'status':
      const progress = logger.getProgress();
      console.log('=== Scraping Status ===');
      console.log(`Status: ${progress.status}`);
      console.log(`Started: ${progress.startedAt}`);
      console.log(`Last Updated: ${progress.lastUpdatedAt}`);
      console.log(`Categories: ${progress.scrapedCategories}/${progress.totalCategories}`);
      console.log(`Products: ${progress.scrapedProducts}/${progress.totalProducts}`);
      if (progress.currentCategory) {
        console.log(`Current Category: ${progress.currentCategory}`);
      }
      if (progress.failedProducts.length > 0) {
        console.log(`Failed Products: ${progress.failedProducts.length}`);
      }
      break;
      
    default:
      console.log(`
🚀 Единый FST Скраппер CLI

Использование:
  bun src/cli.ts [команда]

Команды:
  start     - Запустить или продолжить скрапинг
  reset     - Сбросить прогресс и начать заново
  status    - Показать текущий прогресс скрапинга

Примеры:
  bun src/cli.ts start
  bun src/cli.ts status
  bun src/cli.ts reset

Особенности:
  ✅ Подробное логирование процесса
  ✅ Локальное сохранение изображений
  ✅ Возможность возобновления после прерывания
  ✅ Детальная статистика прогресса
      `);
      break;
  }
}

main().catch(console.error);
