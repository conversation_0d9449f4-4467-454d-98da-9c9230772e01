#!/usr/bin/env bun

/**
 * 🧪 Тест единого скраппера на одном товаре
 * 
 * Этот скрипт тестирует функциональность скрапинга одного товара
 * для проверки корректности работы всех компонентов.
 */

import { chromium } from 'playwright';
import * as fs from 'fs-extra';
import * as path from 'path';

async function testSingleProduct() {
  console.log('🧪 Тестирование скрапинга одного товара...');
  
  const browser = await chromium.launch({
    headless: false,
    timeout: 60000,
    args: [
      '--no-sandbox',
      '--disable-dev-shm-usage',
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor'
    ]
  });
  
  const page = await browser.newPage({
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
  });
  
  try {
    // Тестовый URL товара
    const testProductUrl = 'https://products.fst.com/global/en/categories/radial-shaft-seal-simmerring-baum/products/386014';
    
    console.log(`📦 Переходим к товару: ${testProductUrl}`);
    await page.goto(testProductUrl, { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    });
    
    await page.waitForTimeout(5000);
    
    const productData = await page.evaluate(() => {
      const fstItemNo = window.location.pathname.split('/').pop() || '';
      const title = document.querySelector('h1')?.textContent?.trim() || document.title;
      const description = document.querySelector('meta[name="description"]')?.getAttribute('content') || '';

      const specifications: Record<string, string> = {};
      const dimensions: Record<string, string> = {};

      // Извлекаем характеристики из таблицы
      const detailRows = document.querySelectorAll('.product-details-main__table__row');
      detailRows.forEach(row => {
        const labelEl = row.querySelector('.product-details-main__table__row__label');
        const valueEl = row.querySelector('.product-details-main__table__row__value');
        
        if (labelEl && valueEl) {
          const label = labelEl.textContent?.trim();
          const value = valueEl.textContent?.trim();
          
          if (label && value) {
            if (label.toLowerCase().includes('dimension') || 
                label.toLowerCase().includes('size') ||
                label.toLowerCase().includes('diameter') ||
                label.toLowerCase().includes('length') ||
                label.toLowerCase().includes('width') ||
                label.toLowerCase().includes('height')) {
              dimensions[label] = value;
            } else {
              specifications[label] = value;
            }
          }
        }
      });

      // Извлекаем изображения
      const images: string[] = [];
      const imgElements = document.querySelectorAll('img');
      imgElements.forEach(img => {
        if (img.src &&
            !img.src.includes('data:') &&
            !img.src.includes('icon') &&
            !img.src.includes('logo') &&
            !img.src.includes('sprite')) {
          images.push(img.src);
        }
      });

      return {
        fstItemNo,
        title,
        description,
        specifications,
        dimensions,
        images: Array.from(new Set(images)),
        url: window.location.href,
        scrapedAt: new Date().toISOString()
      };
    });

    console.log('✅ Данные товара извлечены:');
    console.log(`   📦 ID: ${productData.fstItemNo}`);
    console.log(`   📝 Название: ${productData.title}`);
    console.log(`   🔧 Характеристики: ${Object.keys(productData.specifications).length}`);
    console.log(`   📏 Размеры: ${Object.keys(productData.dimensions).length}`);
    console.log(`   📷 Изображения: ${productData.images.length}`);
    
    // Сохраняем результат теста
    const testDir = path.join(__dirname, 'test-results');
    fs.ensureDirSync(testDir);
    
    const testResultPath = path.join(testDir, 'single-product-test.json');
    await fs.writeJson(testResultPath, productData, { spaces: 2 });
    
    console.log(`💾 Результат сохранен в: ${testResultPath}`);
    
    // Тестируем скачивание изображения
    if (productData.images.length > 0) {
      console.log('📷 Тестируем скачивание изображения...');
      
      try {
        const imageUrl = productData.images[0];
        const response = await fetch(imageUrl);
        
        if (response.ok) {
          const buffer = await response.arrayBuffer();
          const imagesDir = path.join(testDir, 'images');
          fs.ensureDirSync(imagesDir);
          
          const imagePath = path.join(imagesDir, 'test-image.jpg');
          await fs.writeFile(imagePath, Buffer.from(buffer));
          
          console.log(`✅ Изображение сохранено: ${imagePath}`);
        } else {
          console.log('⚠️ Не удалось скачать изображение');
        }
      } catch (error) {
        console.log('❌ Ошибка скачивания изображения:', error);
      }
    }
    
    console.log('🎉 Тест завершен успешно!');
    
  } catch (error) {
    console.error('❌ Ошибка теста:', error);
  } finally {
    await browser.close();
  }
}

testSingleProduct().catch(console.error);
